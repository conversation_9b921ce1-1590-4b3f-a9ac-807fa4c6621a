import React, { useState, useRef } from 'react';
import classNames from 'classnames';
import { toggleModalOpen } from 'utils/common';
import TaskCommentsSection from './TaskCommentsSection';
import TaskAttachmentsSection from './TaskAttachmentsSection';
import MOCDetailsSection from './MOCDetailsSection';
import TaskComment, { ITaskCommentGroup } from '../../../types/TaskComment';
import { Task, TaskFile, TaskStatus } from '../../../types/Task';
import { IFile } from '../../../types/common/Item';
import { UserTaskEntity } from 'types/Workflow';
import { useReactToPrint } from 'react-to-print';

interface FileAttachment {
    workflow_step_id: string;
    workflow_step_name: string;
    workflow_step_order: number;
    files: (TaskFile | IFile)[];
}

interface IProps {
    show: boolean;
    changeShow: (show: boolean) => void;
    taskComments: TaskComment[];
    activeTab?: string;
    onSendComment?: (content: string) => void;
    onDeleteComment?: (id: string) => void;
    isSendingComment?: boolean;
    userTaskDetail?: Task;
    groupedTaskComments: ITaskCommentGroup[];
    fileAttachments: FileAttachment[];
    fileLength: number;
}

export default function MOCDetailsModal({
    show,
    changeShow,
    taskComments,
    activeTab = 'moc-details',
    onSendComment,
    onDeleteComment,
    isSendingComment,
    userTaskDetail,
    groupedTaskComments,
    fileAttachments,
    fileLength,
}: Readonly<IProps>) {
    const [currentTab, setCurrentTab] = useState(activeTab);

    React.useLayoutEffect(() => toggleModalOpen(show), [show]);
    React.useEffect(() => {
        setCurrentTab(activeTab);
    }, [activeTab]);

    const handleTabClick = (tabId: string) => {
        setCurrentTab(tabId);
    };

    // Create a separate ref for export content
    const exportContentRef = useRef<HTMLDivElement>(null);

    const handleExportPDF = useReactToPrint({
        contentRef: exportContentRef,
        documentTitle: `MOC-Details-${userTaskDetail?.workflow_instance?.business_key || 'export'}`,
        pageStyle: `
            @page {
                size: A4;
                margin: 20mm;
            }

            @media print {
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-size: 12px;
                    line-height: 1.4;
                    color: #000000 !important;
                    background: white !important;
                }

                * {
                    color: #000000 !important;
                    background-color: transparent !important;
                    border-color: #cccccc !important;
                    box-shadow: none !important;
                }

                .tw-bg-white {
                    background-color: #ffffff !important;
                }

                .tw-bg-gray-50 {
                    background-color: #f9fafb !important;
                }

                .tw-text-gray-900, .tw-text-gray-800, .tw-text-gray-700 {
                    color: #1f2937 !important;
                }

                .tw-text-gray-600, .tw-text-gray-500 {
                    color: #6b7280 !important;
                }

                .tw-text-gray-400 {
                    color: #9ca3af !important;
                }

                .tw-border-gray-300 {
                    border-color: #d1d5db !important;
                }

                .tw-border-gray-200 {
                    border-color: #e5e7eb !important;
                }

                .card {
                    border: 1px solid #cccccc !important;
                    margin-bottom: 10px !important;
                    page-break-inside: avoid;
                }

                .card-header {
                    background-color: #f8f8f8 !important;
                    border-bottom: 1px solid #cccccc !important;
                    padding: 8px 12px !important;
                }

                .card-body {
                    padding: 12px !important;
                }

                .collapsible-content {
                    height: auto !important;
                    opacity: 1 !important;
                    overflow: visible !important;
                }

                .tw-print-container {
                    page-break-inside: auto;
                    break-inside: auto;
                }

                .no-print {
                    display: none !important;
                }
            }
        `,
        onAfterPrint: () => {
            console.log('Export completed successfully!');
        },
    });



    const groupedByTaskKey: Record<string, UserTaskEntity[]> = (userTaskDetail?.workflow_instance?.user_tasks || [])
        .filter((task) => task.status !== TaskStatus.IN_PROGRESS)
        .reduce((acc, task) => {
            const key = task.task_key;
            if (!acc[key]) acc[key] = [];
            acc[key].push(task);
            return acc;
        }, {} as Record<string, UserTaskEntity[]>);

    const renderTabContent = () => {
        switch (currentTab) {
            case 'moc-details':
                return (
                    <MOCDetailsSection
                        userTaskDetail={userTaskDetail}
                        groupedByTaskKey={groupedByTaskKey}
                        forExport={false}
                    />
                );
            case 'comments':
                return (
                    <TaskCommentsSection
                        taskComments={taskComments}
                        onSendComment={onSendComment}
                        onDeleteComment={onDeleteComment}
                        isSendingComment={isSendingComment}
                        userTaskDetail={userTaskDetail}
                        groupedTaskComments={groupedTaskComments}
                    />
                );
            case 'attachments':
                return <TaskAttachmentsSection fileAttachments={fileAttachments} />;
            default:
                return null;
        }
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-dialog-scrollable tw-max-w-[1200px]">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">
                                MOC Details: {userTaskDetail?.workflow_instance?.business_key}
                            </h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <div className="modal-body">
                            {/* Tabs */}
                            <div className="tw-flex tw-items-center tw-justify-between tw-mb-2">
                                <ul className="nav nav-tabs mb-2" role="tablist">
                                    <li className="nav-item">
                                        <button
                                            className={classNames('nav-link cursor-pointer', {
                                                active: currentTab === 'moc-details',
                                            })}
                                            onClick={() => handleTabClick('moc-details')}
                                            role="tab"
                                            aria-selected={currentTab === 'moc-details'}
                                        >
                                            MOC Details
                                        </button>
                                    </li>
                                    <li className="nav-item">
                                        <button
                                            className={classNames('nav-link cursor-pointer', {
                                                active: currentTab === 'comments',
                                            })}
                                            onClick={() => handleTabClick('comments')}
                                            role="tab"
                                            aria-selected={currentTab === 'comments'}
                                        >
                                            Comments ({taskComments?.length})
                                        </button>
                                    </li>
                                    <li className="nav-item">
                                        <button
                                            className={classNames('nav-link cursor-pointer', {
                                                active: currentTab === 'attachments',
                                            })}
                                            onClick={() => handleTabClick('attachments')}
                                            role="tab"
                                            aria-selected={currentTab === 'attachments'}
                                        >
                                            Attachments ({fileLength})
                                        </button>
                                    </li>
                                </ul>
                                <button
                                    type="button"
                                    className="btn btn-primary"
                                    onClick={handleExportPDF}
                                >
                                    Export MOC Details
                                </button>
                            </div>

                            {/* Tab Content */}
                            <div className="tab-content">{renderTabContent()}</div>
                        </div>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}

            {/* Hidden content for export - positioned off-screen */}
            <div
                ref={exportContentRef}
                style={{
                    position: 'absolute',
                    left: '-9999px',
                    top: '0',
                    width: '210mm',
                    backgroundColor: '#ffffff'
                }}
            >
                <div className="tw-print-container">
                    <h2 style={{ marginBottom: '20px', textAlign: 'center' }}>
                        MOC Details: {userTaskDetail?.workflow_instance?.business_key}
                    </h2>
                    <MOCDetailsSection
                        userTaskDetail={userTaskDetail}
                        groupedByTaskKey={groupedByTaskKey}
                        forExport={true}
                    />
                </div>
            </div>
        </>
    );
}
