import React from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import Area from 'types/OperationalArea';
import { STEP_COLORS } from './MOCStepSummary';
import { ProcessMocByArea, TimeLineStackerByStep } from '../../../../types/Dashboard';
import { getColumnWidth } from '../moc_overview/MOCAreaChart';

// Helper function để convert full month name thành short name
const getShortMonthName = (fullName: string): string => {
    const monthMap: { [key: string]: string } = {
        January: 'Jan',
        February: 'Feb',
        March: 'Mar',
        April: 'Apr',
        May: 'May',
        June: 'Jun',
        July: 'Jul',
        August: 'Aug',
        September: 'Sep',
        October: 'Oct',
        November: 'Nov',
        December: 'Dec',
    };
    return monthMap[fullName] || fullName;
};

interface MOCProcessChartsProps {
    areaList?: Area[];
    areaStackerByStep: ProcessMocByArea[];
    timeLineStackerByStep: TimeLineStackerByStep[];
}

const MOCProcessCharts = ({ areaStackerByStep, timeLineStackerByStep }: Readonly<MOCProcessChartsProps>) => {
    // Chuẩn bị data cho left chart (In Progress MOCs by Area)
    const leftChartCategories = areaStackerByStep.map((area) => area.name);

    // Lấy tất cả các step từ area đầu tiên (vì tất cả area đều có đầy đủ steps đã được sort)
    const allSteps = areaStackerByStep.length > 0 ? areaStackerByStep[0].stack : [];

    // Tạo series data cho từng step (left chart)
    const leftChartSeries = allSteps.map((step) => ({
        name: step.name,
        data: areaStackerByStep.map((area) => {
            const stepData = area.stack.find((s) => s.id === step.id);
            return stepData ? stepData.count : 0;
        }),
        color: STEP_COLORS[step.order % STEP_COLORS.length],
    }));

    // Chuẩn bị data cho right chart (MOC Monthly Movement by Step)
    const rightChartCategories = timeLineStackerByStep.map((timeline) => getShortMonthName(timeline.name));

    // Tạo series data cho từng step theo tháng (right chart) - sử dụng allSteps từ areaStackerByStep
    const rightChartSeries = allSteps.map((step) => ({
        name: step.name,
        data: timeLineStackerByStep.map((timeline) => {
            const stepData = timeline.stack.find((s) => s.id === step.id);
            return stepData ? stepData.count : 0;
        }),
        color: STEP_COLORS[step.order % STEP_COLORS.length],
    }));

    const leftDynamicColumnWidth = getColumnWidth(leftChartCategories.length);
    const rightDynamicColumnWidth = getColumnWidth(rightChartCategories.length);

    // Left chart options (In Progress MOCs by Area)
    const leftChartOptions: ApexOptions = {
        chart: {
            type: 'bar',
            stacked: true,
            toolbar: {
                show: false,
            },
            height: 400,
            offsetY: 0,
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: leftDynamicColumnWidth,
                dataLabels: {
                    total: {
                        enabled: true,
                        style: {
                            fontSize: '12px',
                            fontWeight: 'bold',
                            color: '#373d3f',
                        },
                    },
                },
            },
        },
        dataLabels: {
            enabled: false,
        },
        xaxis: {
            categories: leftChartCategories,
            labels: {
                style: {
                    fontSize: '10px',
                },
                rotate: -45,
                rotateAlways: true,
                hideOverlappingLabels: false,
                showDuplicates: false,
                trim: false,
                maxHeight: 120,
            },
            tickAmount: leftChartCategories.length,
        },
        yaxis: {
            title: {
                text: 'Number of In Progress MOCs',
                style: {
                    fontSize: '12px',
                    fontWeight: 'bold',
                },
            },
            labels: {
                style: {
                    fontSize: '12px',
                },
            },
        },
        colors: leftChartSeries.map((series) => series.color),
        legend: {
            position: 'top',
            horizontalAlign: 'right',
            fontSize: '12px',
        },
        grid: {
            show: true,
            borderColor: '#e9ecef',
        },
        title: {
            text: 'In Progress MOCs by Area (Stacked by Step)',
            align: 'center',
            style: {
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#373d3f',
            },
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val.toString();
                },
            },
        },
        responsive: [
            {
                breakpoint: 1000,
                options: {
                    xaxis: {
                        labels: {
                            rotate: -90,
                            style: {
                                fontSize: '9px',
                            },
                        },
                    },
                },
            },
        ],
    };

    // Right chart options (MOC Monthly Movement by Step)
    const rightChartOptions: ApexOptions = {
        chart: {
            type: 'bar',
            stacked: true,
            toolbar: {
                show: false,
            },
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: rightDynamicColumnWidth,
                dataLabels: {
                    total: {
                        enabled: true,
                        style: {
                            fontSize: '12px',
                            fontWeight: 'bold',
                            color: '#373d3f',
                        },
                    },
                },
            },
        },
        dataLabels: {
            enabled: false,
        },
        xaxis: {
            categories: rightChartCategories,
            labels: {
                style: {
                    fontSize: '12px',
                },
            },
        },
        yaxis: {
            title: {
                text: 'Number of MOCs',
                style: {
                    fontSize: '12px',
                    fontWeight: 'bold',
                },
            },
            labels: {
                style: {
                    fontSize: '12px',
                },
            },
        },
        colors: rightChartSeries.map((series) => series.color),
        legend: {
            position: 'top',
            horizontalAlign: 'right',
            fontSize: '12px',
        },
        grid: {
            show: true,
            borderColor: '#e9ecef',
        },
        title: {
            text: 'MOC Monthly Movement by Step (Jan-Dec)',
            align: 'center',
            style: {
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#373d3f',
            },
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val.toString();
                },
            },
        },
    };

    // Hiển thị loading hoặc empty state nếu chưa có data
    if (
        !areaStackerByStep ||
        areaStackerByStep.length === 0 ||
        !timeLineStackerByStep ||
        timeLineStackerByStep.length === 0
    ) {
        return (
            <div className="tw-w-full tw-flex tw-justify-center tw-items-center tw-h-96">
                <div className="tw-text-gray-500 tw-text-lg">Loading chart data...</div>
            </div>
        );
    }

    return (
        <div className="tw-w-full tw-space-y-4">
            {/* Left Chart - In Progress MOCs by Area */}
            <div className="tw-mb-8">
                <Chart options={leftChartOptions} series={leftChartSeries} type="bar" height={450} />
            </div>

            {/* Right Chart - MOC Monthly Movement by Step */}
            <div className="tw-mb-8">
                <Chart options={rightChartOptions} series={rightChartSeries} type="bar" height={400} />
            </div>
        </div>
    );
};

export default MOCProcessCharts;
