import React from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import { OverviewAreaSummary } from '../../../../types/Dashboard';

interface MOCAreaChartProps {
    mocAreaSummaryData: OverviewAreaSummary[];
}

// Tính toán dynamic column width dựa trên số lượng area
export const getColumnWidth = (categoriesLength: number): string => {
    if (categoriesLength <= 1) return '5%';
    if (categoriesLength <= 2) return '10%';
    if (categoriesLength <= 3) return '15%';
    return '20%';
};

const MOCAreaChart: React.FC<MOCAreaChartProps> = ({ mocAreaSummaryData }) => {
    // Chuẩn bị data cho left chart (MOC Summary)
    const leftChartCategories = mocAreaSummaryData.map((area) => area.name);
    const leftChartInProgress = mocAreaSummaryData.map(
        (area) => area.summary.on_tracking + area.summary.overdue + area.summary.backlog
    );
    const leftChartComplete = mocAreaSummaryData.map((area) => area.summary.completed);
    const leftChartCancel = mocAreaSummaryData.map((area) => area.summary.cancelled);

    // Chuẩn bị data cho right chart (Overdue & Backlog)
    const rightChartOverdue = mocAreaSummaryData.map((area) => area.summary.overdue);
    const rightChartBacklog = mocAreaSummaryData.map((area) => area.summary.backlog);

    const dynamicColumnWidth = getColumnWidth(leftChartCategories.length);

    // Left chart options (MOC Summary)
    const leftChartOptions: ApexOptions = {
        chart: {
            type: 'bar',
            stacked: true,
            toolbar: {
                show: false,
            },
            height: 450,
            offsetY: 0,
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: dynamicColumnWidth,
                dataLabels: {
                    total: {
                        enabled: true,
                        style: {
                            fontSize: '12px',
                            fontWeight: 'bold',
                            color: '#373d3f',
                        },
                    },
                },
            },
        },
        dataLabels: {
            enabled: false,
        },
        xaxis: {
            categories: leftChartCategories,
            labels: {
                style: {
                    fontSize: '10px',
                },
                rotate: -45,
                rotateAlways: true,
                hideOverlappingLabels: false,
                showDuplicates: false,
                trim: false,
                maxHeight: 120,
            },
            tickAmount: leftChartCategories.length,
        },
        yaxis: {
            labels: {
                style: {
                    fontSize: '12px',
                },
            },
        },
        colors: ['#007bff', '#28a745', '#ffc107'], // In Progress, Complete, Cancel
        legend: {
            position: 'top',
            horizontalAlign: 'right',
            fontSize: '12px',
        },
        grid: {
            show: true,
            borderColor: '#e9ecef',
        },
        title: {
            text: 'MOC Summary by Area (Stacked Status with Values)',
            align: 'center',
            style: {
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#373d3f',
            },
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val.toString();
                },
            },
        },
        responsive: [
            {
                breakpoint: 1000,
                options: {
                    xaxis: {
                        labels: {
                            rotate: -90,
                            style: {
                                fontSize: '9px',
                            },
                        },
                    },
                },
            },
        ],
    };

    const leftChartSeries = [
        {
            name: 'In Progress',
            data: leftChartInProgress,
        },
        {
            name: 'Complete',
            data: leftChartComplete,
        },
        {
            name: 'Cancel',
            data: leftChartCancel,
        },
    ];

    // Right chart options (Overdue & Backlog)
    const rightChartOptions: ApexOptions = {
        chart: {
            type: 'bar',
            stacked: true,
            toolbar: {
                show: false,
            },
            height: 450,
            offsetY: 0,
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: dynamicColumnWidth,
                dataLabels: {
                    total: {
                        enabled: true,
                        style: {
                            fontSize: '12px',
                            fontWeight: 'bold',
                            color: '#373d3f',
                        },
                    },
                },
            },
        },
        dataLabels: {
            enabled: false,
        },
        xaxis: {
            categories: leftChartCategories,
            labels: {
                style: {
                    fontSize: '10px',
                },
                rotate: -45,
                rotateAlways: true,
                hideOverlappingLabels: false,
                showDuplicates: false,
                trim: false,
                maxHeight: 120,
            },
            tickAmount: leftChartCategories.length,
        },
        yaxis: {
            labels: {
                style: {
                    fontSize: '12px',
                },
            },
        },
        colors: ['#dc3545', '#343a40'], // Overdue, Backlog
        legend: {
            position: 'top',
            horizontalAlign: 'right',
            fontSize: '12px',
        },
        grid: {
            show: true,
            borderColor: '#e9ecef',
        },
        title: {
            text: 'Overdue and Backlog MOCs by Area',
            align: 'center',
            style: {
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#373d3f',
            },
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val.toString();
                },
            },
        },
        responsive: [
            {
                breakpoint: 1000,
                options: {
                    xaxis: {
                        labels: {
                            rotate: -90,
                            style: {
                                fontSize: '9px',
                            },
                        },
                    },
                },
            },
        ],
    };

    const rightChartSeries = [
        {
            name: 'Overdue',
            data: rightChartOverdue,
        },
        {
            name: 'Backlog',
            data: rightChartBacklog,
        },
    ];

    return (
        <div className="tw-w-full tw-grid tw-grid-cols-1 tw-gap-6">
            {/* Left Chart - MOC Summary */}
            <div className="card tw-mb-8">
                <div className="card-body">
                    <Chart options={leftChartOptions} series={leftChartSeries} type="bar" height={450} />
                </div>
            </div>

            {/* Right Chart - Overdue & Backlog */}
            <div className="card tw-mb-8">
                <div className="card-body">
                    <Chart options={rightChartOptions} series={rightChartSeries} type="bar" height={450} />
                </div>
            </div>
        </div>
    );
};

export default MOCAreaChart;
