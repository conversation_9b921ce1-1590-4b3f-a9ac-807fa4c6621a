import { gql } from 'graphql-request';

export const TASK_LIST = gql`
    query User_tasks_by_assignee($page: Int!, $limit: Int!, $filters: [String!], $sort: String, $search: String) {
        user_tasks_by_assignee(body: { page: $page, limit: $limit, filters: $filters, sort: $sort, search: $search }) {
            currentPage
            totalPages
            totalCount
            data {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                workflow_instance_id
                task_id
                task_key
                task_name
                assignee
                status
                started_at
                completed_at
                completed_by
                form_id
                form_data
                variables
                workflow_step_id
                task_no
                tracking_status
                workflow_instance {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at

                    process_instance_id
                    camunda_key
                    business_key
                    name
                    status
                    started_at
                    ended_at
                    form_data
                    camunda_variables
                    current_user_task_id
                }
                workflow_step {
                    id

                    step_key
                    step_name
                    step_order
                    workflow_definition {
                        id
                        created_by
                        updated_by
                        created_at
                        updated_at
                        deleted_at
                        name
                        bpmnXml
                        status
                        camunda_key
                        camunda_id
                        version
                    }
                }
            }
        }
    }
`;
export const TASK_DETAIL = gql`
    query User_task_detail($id: String!) {
        user_task_detail(id: $id) {
            id
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
            workflow_instance_id
            task_id
            task_key
            task_name
            assignee
            status
            started_at
            completed_at
            completed_by
            form_id
            form_data
            variables
            order
            element_variable
            element_value
            subprocess_element_variable
            subprocess_element_value
            workflow_step_id
            tracking_status
            userRoles {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                is_gatekeeper_child
                type
                name
                variable_name
            }
            user_zones {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                type
                name
                code
                parent_area_id
                out_of_service
            }
            updater {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                oms_user_id
                username
                email
                full_name
                is_active
                oms_emp_code
                oms_organization_id
                oms_organization_name
                oms_ad_account
                oms_position_name
                oms_company_code
                oms_company_name
                oms_div_name
                oms_dep_name
                auth_group
            }
            form {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                name
                schema
                description
                status
                version
                root_form_id
            }
            workflow_step {
                id

                step_key
                step_name
                step_order
                workflow_definition {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    name
                    bpmnXml
                    status
                    camunda_key
                    camunda_id
                    version
                    workflow_instances {
                        id
                        created_by
                        updated_by
                        created_at
                        updated_at
                        deleted_at

                        process_instance_id
                        camunda_key
                        business_key
                        name
                        status
                        started_at
                        ended_at
                        form_data
                        camunda_variables
                        current_user_task_id
                        user_tasks {
                            id
                            created_by
                            updated_by
                            created_at
                            updated_at
                            deleted_at
                            workflow_instance_id
                            task_id
                            task_key
                            task_name
                            assignee
                            status
                            started_at
                            completed_at
                            completed_by
                            form_id
                            form_data
                            variables
                            workflow_step_id
                        }
                    }
                    workflow_steps {
                        id

                        step_key
                        step_name
                        step_order
                    }
                    workflow_forms {
                        id

                        form_id
                        task_key
                        task_name
                        is_first_form
                    }
                }
            }
            workflow_instance {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                process_instance_id
                camunda_key
                business_key
                name
                status
                started_at
                ended_at
                form_data
                camunda_variables
                current_user_task_id
                creator {
                    id
                    full_name
                }
                workflow_definition {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    name
                    bpmnXml
                    status
                    camunda_key
                    camunda_id
                    version
                    workflow_steps {
                        id

                        step_key
                        step_name
                        step_order
                    }
                    workflow_forms {
                        id

                        form_id
                        task_key
                        task_name
                        is_first_form
                        form {
                            id
                            created_by
                            updated_by
                            created_at
                            updated_at
                            deleted_at
                            name
                            schema
                            description
                            status
                            version
                            root_form_id
                            creator {
                                id
                                created_by
                                updated_by
                                created_at
                                updated_at
                                deleted_at
                                oms_user_id
                                username
                                email
                                full_name
                                is_active
                                oms_emp_code
                                oms_organization_id
                                oms_organization_name
                                oms_ad_account
                                oms_position_name
                                oms_company_code
                                oms_company_name
                                oms_div_name
                                oms_dep_name
                                auth_group
                            }
                        }
                    }
                }
                user_tasks {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    deleted_at
                    workflow_instance_id
                    task_id
                    task_key
                    task_name
                    assignee
                    status
                    started_at
                    completed_at
                    completed_by
                    form_id
                    form_data
                    variables
                    workflow_step_id
                    order
                    element_variable
                    element_value
                    subprocess_element_variable
                    subprocess_element_value
                    userRoles {
                        id
                        created_by
                        updated_by
                        created_at
                        updated_at
                        deleted_at
                        is_gatekeeper_child
                        type
                        name
                        variable_name
                    }
                    user_zones {
                        id
                        created_by
                        updated_by
                        created_at
                        updated_at
                        deleted_at
                        type
                        name
                        code
                        parent_area_id
                        out_of_service
                    }
                    files {
                        id
                        mime_type
                        file_size
                        file_url
                        file_name
                        created_at
                        creator {
                            id
                            full_name
                        }
                    }
                    workflow_step {
                        id
                        step_key
                        step_name
                        step_order
                    }
                    form {
                        id
                        created_by
                        updated_by
                        created_at
                        updated_at
                        deleted_at
                        name
                        schema
                        description
                        status
                    }
                    workflow_instance {
                        id
                        created_by
                        updated_by
                        created_at
                        updated_at
                        deleted_at

                        process_instance_id
                        camunda_key
                        business_key
                        name
                        status
                        started_at
                        ended_at
                        form_data
                        camunda_variables
                        type
                        sub_area_ids
                        current_user_task_id
                        user_tasks {
                            id
                            created_by
                            updated_by
                            created_at
                            updated_at
                            deleted_at
                            workflow_instance_id
                            task_id
                            task_key
                            task_name
                            assignee
                            status
                            started_at
                            completed_at
                            completed_by
                            form_id
                            form_data
                            variables
                            workflow_step_id
                            order
                            element_variable
                            element_value
                            subprocess_element_variable
                            subprocess_element_value
                            task_no
                            workflow_instance {
                                id
                                created_by
                                updated_by
                                created_at
                                updated_at
                                deleted_at

                                process_instance_id
                                camunda_key
                                business_key
                                name
                                status
                                started_at
                                ended_at
                                form_data
                                camunda_variables
                                type
                                sub_area_ids
                                current_user_task_id
                            }
                        }
                    }
                    assigneeInfo {
                        id
                        created_by
                        updated_by
                        created_at
                        updated_at
                        deleted_at
                        oms_user_id
                        username
                        email
                        full_name
                        is_active
                        oms_emp_code
                        oms_organization_id
                        oms_organization_name
                        oms_ad_account
                        oms_position_name
                        oms_company_code
                        oms_company_name
                        oms_div_name
                        oms_dep_name
                        auth_group
                    }
                }
                files {
                    id
                    file_name
                    file_url
                    file_size
                    mime_type
                    created_at
                    creator {
                        id
                        full_name
                    }
                }
            }
            form {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                name
                schema
                description
                status
            }
            task_comments {
                id
                created_by
                created_at
                user_task_id
                content
                userTask {
                    id
                    task_id
                    task_key
                    workflow_step {
                        id
                        step_key
                        step_name
                    }
                }
                creator {
                    full_name
                }
            }
            files {
                id
                created_by
                file_name
                file_url
                file_size
                userTask {
                    id
                    task_id
                    task_key
                    task_name
                    workflow_step_id
                    workflow_step {
                        id
                        step_key
                        step_name
                    }
                }
                creator {
                    id
                    full_name
                }
            }
        }
    }
`;

export const COMPLETE_TASK = gql`
    mutation complete_task($input: CompleteTaskInput!) {
        complete_task(input: $input) {
            id
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
            workflow_instance_id
            task_id
            task_key
            task_name
            assignee
            status
            started_at
            completed_at
            completed_by
            form_id
            form_data
            variables
            workflow_step_id
        }
    }
`;

export const USER_TASK_DASHBOARD = `
    query User_tasks_dashboard {
        user_tasks_dashboard {
            in_progress
            completed
            cancelled
            overdue
            backlog
        }
    }
`;

export const DELEGATE_TASK = gql`
    mutation DelegateTask($input: DelegateTaskInputDto!) {
        delegate_task(input: $input) {
            id
            created_by
            updated_by
            created_at
            updated_at
            deleted_at
            workflow_instance_id
            task_id
            task_key
            task_name
            assignee
            status
            started_at
            completed_at
            completed_by
            form_id
            form_data
            variables
            workflow_step_id
            workflow_instance_step_id
            order
            assignee_variable
            element_variable
            element_value
            subprocess_element_variable
            subprocess_element_value
            tracking_status
            task_no
            user_role_ids
            user_zone_ids
        }
    }
`;
