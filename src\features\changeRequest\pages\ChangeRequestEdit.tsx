import { Link, useNavigate, useParams } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import ContentHeader from 'components/partials/ContentHeader';
import Spinner from 'components/partials/Spinner';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { WorkflowInstanceDetailQuery } from 'types/Workflow';
import { QUERY_KEY } from 'constants/common';
import { WORKFLOW_INSTANCE_DETAIL } from 'services/WorkflowService';
import { keepPreviousData } from '@tanstack/react-query';
import { useAuthStore } from 'stores/authStore';
import FormFields from 'features/form/components/FormFields';
import { FormItem } from 'types/Form';
import classNames from 'classnames';

export default function ChangeRequestEdit() {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { id } = useParams<{ id: string }>();
    const user = useAuthStore((state) => state.user);

    const onBack = () => {
        navigate('/changeRequest');
    };

    const { data, isLoading } = useGraphQLQuery<WorkflowInstanceDetailQuery>(
        [QUERY_KEY.WORKFLOWS_INSTANCE_DETAIL, id],
        WORKFLOW_INSTANCE_DETAIL,
        { id },
        '',
        {
            enabled: !!id,
            placeholderData: keepPreviousData,
        }
    );

    const getFormInitial = data?.workflow_instances_detail?.workflow_definition?.workflow_forms?.find(
        (item) => item.is_first_form
    )?.form?.schema;
    const getFormDataInitial = data?.workflow_instances_detail?.form_data;

    return (
        <>
            <Helmet>
                <title>{t('Edit Change Request')}</title>
            </Helmet>
            <ContentHeader
                title={
                    <>
                        <Link to="/changeRequest">Change Request</Link>
                    </>
                }
                breadcrumbs={[{ text: 'Edit Change Request' }]}
            />
            <div className="tw-flex tw-items-center tw-gap-6">
                <div className="card tw-mb-0 tw-p-6 tw-w-full tw-flex tw-flex-row tw-justify-start tw-gap-6 tw-flex-wrap">
                    <div className="tw-flex tw-items-center tw-w-max">
                        <p className="tw-text-xl tw-font-medium">MOC Progress:</p>
                    </div>
                    {data?.workflow_instances_detail?.workflow_definition?.workflow_steps?.map((item, index) => (
                        <div className="tw-flex tw-flex-col tw-items-center tw-w-max tw-gap-2" key={item.id}>
                            <div
                                className={`tw-w-8 tw-h-8 tw-rounded-full tw-flex tw-items-center tw-justify-center ${
                                    index === 0 ? 'tw-bg-[#ff9f43]' : 'rounded-pill bg-light-secondary'
                                }`}
                            >
                                <p
                                    className={classNames(`tw-text-center tw-font-medium`, {
                                        'tw-text-white': index === 0,
                                    })}
                                >
                                    {index + 1}
                                </p>
                            </div>
                            <p className="tw-max-w-[120px] tw-break-words tw-text-center">{item.step_name}</p>
                        </div>
                    ))}
                </div>
            </div>
            <div className="content-body">
                <div className="col-12">
                    {isLoading ? (
                        <Spinner />
                    ) : (
                        <div className="tw-w-full mt-2">
                            <div className="card tw-p-6">
                                <div className="content-body">
                                    <div className="card tw-mb-0">
                                        <div className="row tw-pointer-events-none">
                                            <div className="col-12">
                                                <h2 className="card-title tw-py-2 tw-border-b tw-border-b-[#e6e6e8] ">
                                                    Originator Info
                                                </h2>
                                            </div>
                                            <div className="col-md-6 mb-1">
                                                <label className="form-label tw-text-[14px]" htmlFor="originator">
                                                    Originator Name
                                                </label>
                                                <input
                                                    id="originator"
                                                    type="text"
                                                    className={`form-control`}
                                                    disabled
                                                    value={user?.full_name}
                                                />
                                            </div>
                                            <div className="col-md-6 mb-1">
                                                <label className="form-label tw-text-[14px]" htmlFor="originator">
                                                    Position
                                                </label>
                                                <input
                                                    id="originator"
                                                    type="text"
                                                    className={`form-control`}
                                                    disabled
                                                    value={user?.oms_position_name}
                                                />
                                            </div>
                                            <div className="col-md-6 mb-1">
                                                <label className="form-label tw-text-[14px]" htmlFor="originator">
                                                    Department
                                                </label>
                                                <input
                                                    id="originator"
                                                    type="text"
                                                    className={`form-control`}
                                                    disabled
                                                    value={user?.oms_dep_name}
                                                />
                                            </div>
                                            <div className="col-md-6 mb-1">
                                                <label className="form-label tw-text-[14px]" htmlFor="originator">
                                                    Division
                                                </label>
                                                <input
                                                    id="originator"
                                                    type="text"
                                                    className={`form-control`}
                                                    disabled
                                                    value={user?.oms_div_name}
                                                />
                                            </div>
                                            <div className="col-md-6 mb-1">
                                                <label className="form-label tw-text-[14px]" htmlFor="originator">
                                                    Email
                                                </label>
                                                <input
                                                    id="originator"
                                                    type="text"
                                                    className={`form-control`}
                                                    disabled
                                                    value={user?.email}
                                                />
                                            </div>
                                            <div className="col-md-6 mb-1">
                                                <label className="form-label tw-text-[14px]" htmlFor="originator">
                                                    Contact Tel
                                                </label>
                                                <input
                                                    id="originator"
                                                    type="text"
                                                    className={`form-control`}
                                                    disabled
                                                    value={user?.oms_tel || user?.oms_mobileNo}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="card tw-p-10">
                                <div className="content-body">
                                    {getFormInitial && (
                                        <FormFields
                                            initialData={getFormDataInitial}
                                            schemaForm={getFormInitial as FormItem}
                                        />
                                    )}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
