import { ChangeEvent, MouseEvent, useEffect, useMemo, useState } from 'react';
import { ArrowLeft, Plus, X } from 'react-feather';
import { ReactSortable } from 'react-sortablejs';
import './index.css';
import { nanoid } from 'nanoid';
import Select, { MultiValue, SingleValue } from 'react-select';
import ColorPicker from 'components/partials/ColorPicker';
import {
    ButtonType,
    FormFields,
    FormFieldsCheckboxOption,
    FormFieldsDefaultType,
    FormItem,
    FormQuery,
    InputType,
    OptionButtonType,
    OptionFormAlignmentType,
    OptionFormDataSource,
} from 'types/Form';
import classNames from 'classnames';
import { MAX_WIDTH, MIN_WIDTH, QUERY_KEY } from 'constants/common';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { keepPreviousData } from '@tanstack/react-query';
import { useAppStore } from 'stores/appStore';
import { FORM_LIST } from 'services/FormService';
import ReactQuillEditor from '../../../components/partials/ReactQuillEditor';

const options: OptionButtonType[] = [
    { value: ButtonType.CANCEL, label: 'Cancel' },
    { value: ButtonType.APPROVE, label: 'Approve' },
    { value: ButtonType.REJECT, label: 'Reject' },
    { value: ButtonType.TERMINATE, label: 'Terminate' },
    { value: ButtonType.SAVE_DRAFT, label: 'Save draft' },
    { value: ButtonType.SUBMIT, label: 'Submit' },
];

const optionsFormAlignment: OptionFormAlignmentType[] = [
    { value: 'left', label: 'Left' },
    { value: 'center', label: 'Center' },
    { value: 'right', label: 'Right' },
];

const optionsDataSource: OptionFormDataSource[] = [
    { value: 'custom', label: 'Custom' },
    { value: 'pssr_list', label: 'PSSR list' },
];

interface FormDraggableListProps {
    schemaForm: FormItem;
    setSchemaForm(schemaForm: FormItem): void;
}

export const FormDraggableList = ({ schemaForm, setSchemaForm }: FormDraggableListProps) => {
    const activeFields = useAppStore((state) => state.activeFields);
    const setActiveFields = useAppStore((state) => state.setActiveFields);
    const listOption = useAppStore((state) => state.listOption);
    const setListOption = useAppStore((state) => state.setListOption);
    const activeOption = useAppStore((state) => state.activeOption);
    const setActiveOption = useAppStore((state) => state.setActiveOption);

    const [isTabFields, setIsFields] = useState(true);

    const [fieldKeyAutoGen, setFieldKeyAutoGen] = useState<{ [fieldId: string]: boolean }>({});

    const [previousLabels, setPreviousLabels] = useState<{ [fieldId: string]: string }>({});

    const [optionValueAutoGen, setOptionValueAutoGen] = useState<{ [optionId: string]: boolean }>({});

    const [previousOptionLabels, setPreviousOptionLabels] = useState<{ [optionId: string]: string }>({});

    const [previousOptionValues, setPreviousOptionValues] = useState<{ [optionId: string]: string }>({});

    const [duplicateValueStatus, setDuplicateValueStatus] = useState<{ [optionId: string]: boolean }>({});

    const { data: dataForm } = useGraphQLQuery<FormQuery>(
        [QUERY_KEY.FORM_LIST],
        FORM_LIST,
        {
            page: 1,
            limit: 10000,
            search: undefined,
            sort: undefined,
            filters: ['status:=(ACTIVE)'],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const dataListForm: FormFieldsCheckboxOption[] = useMemo(() => {
        if (!dataForm?.form_list?.data) return [];

        return dataForm?.form_list?.data?.map((form) => ({
            id: form.id,
            value: form.id,
            label: form.name,
        }));
    }, [dataForm]);

    const cleanItem = (item: any) => {
        const { chosen, selected, ...rest } = item;
        return rest;
    };

    const [list, setList] = useState(schemaForm?.form_fields || []);

    useEffect(() => {
        if (schemaForm?.form_fields) {
            setList(schemaForm.form_fields);
        } else {
            setList(schemaForm?.form_fields);
        }
    }, [schemaForm?.form_fields]);

    const fieldsDefault: FormFieldsDefaultType[] = [
        {
            id: nanoid(),
            type: 'text',
            title: 'Input text',
        },
        {
            id: nanoid(),
            type: 'number',
            title: 'Input number',
        },
        {
            id: nanoid(),
            type: 'date',
            title: 'Input date',
        },
        {
            id: nanoid(),
            type: 'date_range',
            title: 'Input date range',
        },
        {
            id: nanoid(),
            type: 'time',
            title: 'Input time',
        },
        {
            id: nanoid(),
            type: 'text_area',
            title: 'Input text area',
        },
        {
            id: nanoid(),
            type: 'checkbox',
            title: 'Checkbox',
        },
        {
            id: nanoid(),
            type: 'radio',
            title: 'Radio',
        },
        {
            id: nanoid(),
            type: 'select',
            title: 'Select',
        },
        {
            id: nanoid(),
            type: 'free_text',
            title: 'Free text',
        },
        {
            id: nanoid(),
            type: 'file',
            title: 'File',
        },
        {
            id: nanoid(),
            type: 'area_of_implementation',
            title: 'Area of Implementation',
            quantity: 1,
        },
        {
            id: nanoid(),
            type: 'select_gate_keepers',
            title: 'Select Gate Keepers',
            quantity: 1,
        },
        {
            id: nanoid(),
            type: 'aims_system',
            title: 'AIMS system',
        },
        {
            id: nanoid(),
            type: 'e_smart_iso_system',
            title: 'E-smart ISO system',
        },
        {
            id: nanoid(),
            type: 'pha_database',
            title: 'PHA Database',
        },
        {
            id: nanoid(),
            type: 'proposed_date',
            title: 'Proposed Date',
        },
        {
            id: nanoid(),
            type: 'tooltip',
            title: 'Tooltip',
        },
        {
            id: nanoid(),
            type: 'button',
            title: 'Button',
        },
    ];

    const inputTextDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'text',
        width: '100',
        height: '40',
        label: 'Input text',
        placeholder: '',
        keyAppear: '',
        valueAppear: '',
        isVariable: false,
        isTable: false,
        isFirstAreaOwner: '',
        tooltip: `<p><br></p>`,
    };

    const inputNumberDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'number',
        width: '100',
        height: '40',
        label: 'Input number',
        placeholder: '',
        keyAppear: '',
        valueAppear: '',
        isVariable: false,
        isTable: false,
        isFirstAreaOwner: '',
        tooltip: `<p><br></p>`,
    };

    const inputDateDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'date',
        width: '100',
        height: '40',
        label: 'Input date',
        keyAppear: '',
        valueAppear: '',
        isVariable: false,
        isTable: false,
        isFirstAreaOwner: '',
        tooltip: `<p><br></p>`,
    };

    const inputDateRangeDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'date_range',
        width: '100',
        height: '40',
        label: 'Input date range',
        keyAppear: '',
        valueAppear: '',
        isVariable: false,
        isTable: false,
        isFirstAreaOwner: '',
        tooltip: `<p><br></p>`,
    };

    const inputTimeDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'time',
        width: '100',
        height: '40',
        label: 'Input time',
        keyAppear: '',
        valueAppear: '',
        isVariable: false,
        isTable: false,
        isFirstAreaOwner: '',
        tooltip: `<p><br></p>`,
    };

    const inputTextAreaDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'text_area',
        width: '100',
        height: '80',
        label: 'Input text area',
        keyAppear: '',
        valueAppear: '',
        keyRequired: '',
        valueRequired: [],
        isVariable: false,
        isTable: false,
        isFirstAreaOwner: '',
        isRemark: false,
        tooltip: `<p><br></p>`,
    };

    const inputCheckboxDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'checkbox',
        width: '100',
        label: 'Checkbox',
        option: [{ id: nanoid(), value: 'option_1', label: 'Option 1' }],
        keyAppear: '',
        valueAppear: '',
        dataSource: 'custom',
        isVariable: false,
        isTable: false,
        isFirstAreaOwner: '',
        tooltip: `<p><br></p>`,
    };

    const inputRadioDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'radio',
        width: '100',
        label: 'Radio',
        option: [{ id: nanoid(), value: 'option_1', label: 'Option 1' }],
        keyAppear: '',
        valueAppear: '',
        dataSource: 'custom',
        isVariable: false,
        isTable: false,
        isFirstAreaOwner: '',
        tooltip: `<p><br></p>`,
    };

    const inputSelectDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'select',
        width: '100',
        height: '40',
        label: 'Select',
        option: [{ id: nanoid(), value: 'option_1', label: 'Option 1' }],
        keyAppear: '',
        valueAppear: '',
        dataSource: 'custom',
        isVariable: false,
        isTable: false,
        isFirstAreaOwner: '',
        tooltip: `<p><br></p>`,
        defaultData: '',
    };

    const inputFreeTextDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'free_text',
        width: '100',
        label: `
        <p><span style="color: rgb(110, 107, 123); font-size: 12px;">Write content...<span class="ql-cursor"></span></span></p>
      `,
        keyAppear: '',
        valueAppear: '',
        isVariable: false,
        isTable: false,
        isFirstAreaOwner: '',
    };

    const planAndAreaUnitDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'area_of_implementation',
        label: 'Area of Implementation',
        width: '100',
        keyAppear: '',
        valueAppear: '',
        isVariable: true,
        tooltip: `<p><br></p>`,
    };

    const selectGateKeepersDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'select_gate_keepers',
        label: 'Select Gate Keepers',
        width: '100',
        keyAppear: '',
        valueAppear: '',
        isVariable: false,
        isRequired: true,
        tooltip: `<p><br></p>`,
    };

    const aimsSystemDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'aims_system',
        label: 'AIMS system',
        width: '100',
        keyAppear: '',
        valueAppear: '',
        isRequired: true,
        isVariable: false,
        tooltip: `<p><br></p>`,
    };

    const eSmartIsoSystemDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'e_smart_iso_system',
        label: 'E-smart ISO system',
        width: '100',
        isRequired: true,
        keyAppear: '',
        valueAppear: '',
        isVariable: false,
        tooltip: `<p><br></p>`,
    };

    const phaDatabaseDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'pha_database',
        label: 'PHA Database',
        width: '100',
        isRequired: true,
        keyAppear: '',
        valueAppear: '',
        isVariable: false,
        tooltip: `<p><br></p>`,
    };

    const fileDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'file',
        label: 'File',
        width: '100',
        value: '',
        keyAppear: '',
        valueAppear: '',
        isVariable: false,
        isFirstAreaOwner: '',
        tooltip: `<p><br></p>`,
    };

    const pssrReportDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'pssr_report',
        label: 'Complete the PSSR checklist Report for each stage*',
        width: '100',
        keyAppear: '',
        valueAppear: '',
        isVariable: false,
        tooltip: `<p><br></p>`,
    };

    const closeOutChecklistDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'close_out_checklist',
        label: 'Close Out Checklist*',
        width: '100',
        keyAppear: '',
        valueAppear: '',
        isVariable: false,
        tooltip: `<p><br></p>`,
    };

    const validateChecklistDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'validate_checklist',
        label: 'Validate checklist',
        width: '100',
        keyAppear: '',
        valueAppear: '',
        isVariable: false,
        tooltip: `<p><br></p>`,
    };

    const tooltipDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'tooltip',
        width: '10',
        label: `
        <p><span style="color: rgb(110, 107, 123); font-size: 12px;">Write content...<span class="ql-cursor"></span></span></p>
      `,
        isVariable: false,
    };

    const buttonDefault: FormFields = {
        id: nanoid(),
        key: '',
        type: 'button',
        width: '20',
        buttonType: ButtonType.APPROVE,
        label: 'Button',
        isVariable: false,
        variableKey: '',
        variableValue: '',
    };

    const inputProposedDate: FormFields = {
        id: nanoid(),
        key: 'propose_date',
        type: 'proposed_date',
        width: '100',
        height: '40',
        label: 'Propose Date',
        keyAppear: '',
        valueAppear: '',
        isRequired: true,
    };

    const generateKeyFromLabel = (label: string): string => {
        if (!label || label.trim() === '') return '';

        const cleanLabel = label.replace(/<[^>]*>/g, '');

        return cleanLabel
            .trim()
            .replace(/\s+/g, '_')
            .replace(/[^a-zA-Z0-9_]/g, '')
            .replace(/_+/g, '_')
            .replace(/^_|_$/g, '');
    };

    const isFieldKeyUnique = (key: string, fieldId: string): boolean => {
        if (!schemaForm?.form_fields) return true;

        const allFields = schemaForm.form_fields.filter((field) => field.id !== fieldId);
        return !allFields.some((field) => field.key === key);
    };

    const generateUniqueKeyFromLabel = (label: string, fieldId: string): string => {
        const baseKey = generateKeyFromLabel(label);
        if (!baseKey) return '';

        let uniqueKey = baseKey;
        let counter = 1;

        while (!isFieldKeyUnique(uniqueKey, fieldId)) {
            uniqueKey = `${baseKey}_${counter}`;
            counter++;
        }

        return uniqueKey;
    };

    const generateValueFromLabel = (label: string): string => {
        if (!label || label.trim() === '') return '';

        const cleanLabel = label.replace(/<[^>]*>/g, '');

        return cleanLabel
            .trim()
            .toLowerCase()
            .replace(/\s+/g, '_')
            .replace(/[^a-zA-Z0-9_]/g, '')
            .replace(/_+/g, '_')
            .replace(/^_|_$/g, '');
    };

    const isOptionValueUnique = (
        value: string,
        optionId: string,
        fieldOptions: FormFieldsCheckboxOption[]
    ): boolean => {
        if (!fieldOptions) return true;
        return !fieldOptions.some((option) => option.id !== optionId && option.value === value);
    };

    const generateUniqueValueFromLabel = (
        label: string,
        optionId: string,
        fieldOptions: FormFieldsCheckboxOption[]
    ): string => {
        const baseValue = generateValueFromLabel(label);
        if (!baseValue) return '';

        let uniqueValue = baseValue;
        let counter = 1;

        while (!isOptionValueUnique(uniqueValue, optionId, fieldOptions)) {
            uniqueValue = `${baseValue}_${counter}`;
            counter++;
        }

        return uniqueValue;
    };

    const getNextIndexForOption = (fieldOptions: FormFieldsCheckboxOption[]): number => {
        if (!fieldOptions || fieldOptions.length === 0) {
            return 1;
        }

        let maxIndex = 0;

        fieldOptions.forEach((option) => {
            if (option.label) {
                const labelMatch = option.label.match(/\s+(\d+)$/);
                if (labelMatch) {
                    const labelIndex = parseInt(labelMatch[1], 10);
                    maxIndex = Math.max(maxIndex, labelIndex);
                }
            }
        });

        return maxIndex + 1;
    };

    const onOpenAddField = () => {
        setActiveOption(null);
        setActiveFields(null);
        setIsFields(!isTabFields);
    };

    const getNextIndexForType = (type: InputType): number => {
        const fieldsOfSameType = schemaForm.form_fields.filter((field) => field.type === type);

        if (fieldsOfSameType.length === 0) {
            return 1;
        }

        let maxIndex = 0;

        fieldsOfSameType.forEach((field) => {
            if (field.label && field.type !== 'free_text' && field.type !== 'tooltip') {
                const labelMatch = field.label.match(/\s+(\d+)$/);
                if (labelMatch) {
                    const labelIndex = parseInt(labelMatch[1], 10);
                    maxIndex = Math.max(maxIndex, labelIndex);
                }
            }
        });

        return maxIndex + 1;
    };

    const createFieldWithIndex = (defaultField: FormFields, type: InputType): FormFields => {
        const index = getNextIndexForType(type);
        const baseLabel = defaultField.label || '';
        const newId = nanoid();

        let labelWithIndex = baseLabel;
        if (type !== 'free_text' && type !== 'tooltip') {
            const isHtmlContent = /<[^>]*>/.test(baseLabel);

            if (baseLabel && !isHtmlContent && baseLabel.trim() !== '') {
                labelWithIndex = `${baseLabel} ${index}`;
            }
        }

        let newKey: string;
        const keyFromLabel = generateUniqueKeyFromLabel(labelWithIndex, newId);
        newKey = keyFromLabel || nanoid();

        const newField = {
            ...defaultField,
            id: newId,
            key: newKey,
            label: labelWithIndex,
        };

        setFieldKeyAutoGen((prev) => ({ ...prev, [newId]: true }));
        setPreviousLabels((prev) => ({ ...prev, [newId]: labelWithIndex }));

        if (newField.option && (type === 'checkbox' || type === 'radio' || type === 'select')) {
            newField.option.forEach((option) => {
                setOptionValueAutoGen((prev) => ({ ...prev, [option.id]: true }));
                setPreviousOptionLabels((prev) => ({ ...prev, [option.id]: option.label }));
                setPreviousOptionValues((prev) => ({ ...prev, [option.id]: option.value }));
            });
        }

        return newField;
    };

    const onAddFields = (type: InputType) => {
        setIsFields(true);

        let newField: FormFields;

        if (type === 'text') {
            newField = createFieldWithIndex(inputTextDefault, type);
        } else if (type === 'number') {
            newField = createFieldWithIndex(inputNumberDefault, type);
        } else if (type === 'date') {
            newField = createFieldWithIndex(inputDateDefault, type);
        } else if (type === 'proposed_date') {
            newField = createFieldWithIndex(inputProposedDate, type);
        } else if (type === 'date_range') {
            newField = createFieldWithIndex(inputDateRangeDefault, type);
        } else if (type === 'time') {
            newField = createFieldWithIndex(inputTimeDefault, type);
        } else if (type === 'text_area') {
            newField = createFieldWithIndex(inputTextAreaDefault, type);
        } else if (type === 'checkbox') {
            newField = createFieldWithIndex(inputCheckboxDefault, type);
        } else if (type === 'radio') {
            newField = createFieldWithIndex(inputRadioDefault, type);
        } else if (type === 'select') {
            newField = createFieldWithIndex(inputSelectDefault, type);
        } else if (type === 'free_text') {
            newField = createFieldWithIndex(inputFreeTextDefault, type);
        } else if (type === 'area_of_implementation') {
            newField = createFieldWithIndex(planAndAreaUnitDefault, type);
        } else if (type === 'select_gate_keepers') {
            newField = createFieldWithIndex(selectGateKeepersDefault, type);
        } else if (type === 'aims_system') {
            newField = createFieldWithIndex(aimsSystemDefault, type);
        } else if (type === 'e_smart_iso_system') {
            newField = createFieldWithIndex(eSmartIsoSystemDefault, type);
        } else if (type === 'pha_database') {
            newField = createFieldWithIndex(phaDatabaseDefault, type);
        } else if (type === 'file') {
            newField = createFieldWithIndex(fileDefault, type);
        } else if (type === 'pssr_report') {
            newField = createFieldWithIndex(pssrReportDefault, type);
        } else if (type === 'close_out_checklist') {
            newField = createFieldWithIndex(closeOutChecklistDefault, type);
        } else if (type === 'validate_checklist') {
            newField = createFieldWithIndex(validateChecklistDefault, type);
        } else if (type === 'tooltip') {
            newField = createFieldWithIndex(tooltipDefault, type);
        } else if (type === 'button') {
            newField = createFieldWithIndex(buttonDefault, type);
        } else {
            newField = createFieldWithIndex(inputTextDefault, 'text');
        }

        const updatedFields = [...schemaForm.form_fields, newField];
        setSchemaForm({ ...schemaForm, form_fields: updatedFields });
        setList(updatedFields);
    };

    const onRemoveFields = (e: MouseEvent, id: string) => {
        e.stopPropagation();
        const filteredData = schemaForm?.form_fields.filter((item) => item.id !== id);

        setSchemaForm({ ...schemaForm, form_fields: filteredData });
        setList(filteredData);

        if (activeFields?.id === id) {
            setActiveFields(null);
        }

        setFieldKeyAutoGen((prev) => {
            const newState = { ...prev };
            delete newState[id];
            return newState;
        });
        setPreviousLabels((prev) => {
            const newState = { ...prev };
            delete newState[id];
            return newState;
        });
    };

    const onSelectFields = (e: MouseEvent, fields: FormFields) => {
        e.stopPropagation();
        setActiveFields(activeFields?.id === fields.id ? null : fields);
        setListOption(fields?.option || []);
        setActiveOption(null);
    };

    const onChangeFormTitle = (e: ChangeEvent<HTMLInputElement>) => {
        const newTitle = e.target.value;
        setSchemaForm({
            ...schemaForm,
            settings: {
                ...schemaForm?.settings,
                title: newTitle,
            },
        });
    };

    const onChangeFieldVariable = (value: boolean, field: FormFields) => {
        if (!schemaForm?.form_fields) return;

        const updatedFields = schemaForm.form_fields.map((f) => (f.id === field.id ? { ...f, isVariable: value } : f));

        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });

        if (activeFields?.id === field.id) {
            setActiveFields({ ...field, isVariable: value });
        }

        setList(updatedFields);
    };

    const onChangeFieldRequired = (value: boolean, field: FormFields) => {
        if (!schemaForm?.form_fields) return;

        // Prevent changing required status for select_gate_keepers field
        if (field.type === 'select_gate_keepers') return;

        const updatedFields = schemaForm.form_fields.map((f) => (f.id === field.id ? { ...f, isRequired: value } : f));

        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });
        if (activeFields?.id === field.id) {
            setActiveFields({ ...field, isRequired: value });
        }
        setList(updatedFields);
    };
    const onChangeFieldTable = (value: boolean, field: FormFields) => {
        if (!schemaForm?.form_fields) return;

        const updatedFields = schemaForm.form_fields.map((f) => (f.id === field.id ? { ...f, isTable: value } : f));

        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });
        if (activeFields?.id === field.id) {
            setActiveFields({ ...field, isTable: value });
        }
        setList(updatedFields);
    };

    const onChangeFieldFirstAreaOwner = (value: string, field: FormFields) => {
        if (!schemaForm?.form_fields) return;

        const trimmedValue = value.trim();

        const updatedFields = schemaForm.form_fields.map((f) =>
            f.id === field.id ? { ...f, isFirstAreaOwner: trimmedValue } : f
        );

        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });
        if (activeFields?.id === field.id) {
            setActiveFields({ ...field, isFirstAreaOwner: trimmedValue });
        }
        setList(updatedFields);
    };
    const onChangeFieldTableResult = (value: boolean, field: FormFields) => {
        if (!schemaForm?.form_fields) return;

        const updatedFields = schemaForm.form_fields.map((f) => (f.id === field.id ? { ...f, isRemark: value } : f));

        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });
        if (activeFields?.id === field.id) {
            setActiveFields({ ...field, isRemark: value });
        }
        setList(updatedFields);
    };

    const onChangeFieldWidth = (value: string, field: FormFields) => {
        const updatedFields = schemaForm?.form_fields.map((f) => (f.id === field.id ? { ...f, width: value } : f));
        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });
        if (activeFields?.id === field.id) {
            setActiveFields({ ...field, width: value });
        }
        setList(updatedFields);
    };

    const onBlurFieldWidth = (field: FormFields) => {
        const currentWidth = parseInt(field.width || '', 10);
        let validWidth = currentWidth;

        if (isNaN(currentWidth)) {
            validWidth = MIN_WIDTH;
        } else {
            validWidth = Math.min(Math.max(currentWidth, MIN_WIDTH), MAX_WIDTH);
        }

        if (validWidth.toString() !== field.width) {
            onChangeFieldWidth(validWidth.toString(), field);
        }
    };
    const onChangeFieldLabel = (value: string, field: FormFields) => {
        const updatedField = { ...field, label: value };

        const isAutoGenKey = fieldKeyAutoGen[field.id];

        if (isAutoGenKey) {
            if (value.trim() === '') {
                const prevLabel = previousLabels[field.id] || '';
                const keyFromPrevLabel = generateUniqueKeyFromLabel(prevLabel, field.id);
                updatedField.key = keyFromPrevLabel || nanoid();
            } else {
                const newKey = generateUniqueKeyFromLabel(value, field.id);
                if (newKey) {
                    updatedField.key = newKey;
                }
            }

            if (value.trim() !== '') {
                setPreviousLabels((prev) => ({ ...prev, [field.id]: value }));
            }
        }

        const updatedFields = schemaForm?.form_fields.map((f) => (f.id === field.id ? updatedField : f));
        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });
        if (activeFields?.id === field.id) {
            setActiveFields(updatedField);
        }
        setList(updatedFields);
    };

    const onChangeFieldDefaultData = (value: string, field: FormFields) => {
        const updatedFields = schemaForm?.form_fields.map((f) =>
            f.id === field.id ? { ...f, defaultData: value.trim() } : f
        );
        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });
        if (activeFields?.id === field.id) {
            setActiveFields({ ...field, defaultData: value.trim() });
        }
        setList(updatedFields);
    };

    const onChangeFieldHeight = (value: string, field: FormFields) => {
        const updatedFields = schemaForm?.form_fields.map((f) => (f.id === field.id ? { ...f, height: value } : f));
        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });
        if (activeFields?.id === field.id) {
            setActiveFields({ ...field, height: value });
        }
        setList(updatedFields);
    };

    const onBlurFieldHeight = (field: FormFields) => {
        const currentHeight = parseInt(field.height || '', 10);
        let validHeight = currentHeight;

        if (isNaN(currentHeight)) {
            validHeight = MIN_WIDTH;
        } else {
            validHeight = Math.min(Math.max(currentHeight, MIN_WIDTH), MAX_WIDTH);
        }

        if (validHeight.toString() !== field.height) {
            onChangeFieldHeight(validHeight.toString(), field);
        }
    };

    const onRemoveOption = (e: MouseEvent, id: string) => {
        e.stopPropagation();

        const getListOption = schemaForm?.form_fields.find((item) => item.id === activeFields?.id)?.option;
        const newData = getListOption?.filter((item) => item.id !== id);

        const updatedForm = {
            ...schemaForm,
            form_fields: schemaForm?.form_fields.map((field) =>
                field.id === activeFields?.id ? { ...field, option: newData } : field
            ),
        };

        setSchemaForm(updatedForm);

        newData && setListOption(newData);
        if (activeOption?.id === id) {
            setActiveOption(null);
        }

        setOptionValueAutoGen((prev) => {
            const newState = { ...prev };
            delete newState[id];
            return newState;
        });
        setPreviousOptionLabels((prev) => {
            const newState = { ...prev };
            delete newState[id];
            return newState;
        });
        setPreviousOptionValues((prev) => {
            const newState = { ...prev };
            delete newState[id];
            return newState;
        });
        setDuplicateValueStatus((prev) => {
            const newState = { ...prev };
            delete newState[id];
            return newState;
        });
    };

    const onAddOption = () => {
        if (!activeFields) return;

        const currentField = schemaForm?.form_fields.find((item) => item.id === activeFields.id);
        if (!currentField) return;

        const getListOption = currentField.option || [];
        const index = getNextIndexForOption(getListOption);
        const newId = nanoid();
        const baseLabel = 'Option';
        const labelWithIndex = `${baseLabel} ${index}`;
        const valueFromLabel = generateUniqueValueFromLabel(labelWithIndex, newId, getListOption);

        const newOption = {
            id: newId,
            value: valueFromLabel || `option_${index}`,
            label: labelWithIndex,
        };

        const newData = [...getListOption, newOption];

        const updatedFormFields = schemaForm?.form_fields.map((field) =>
            field.id === currentField.id ? { ...field, option: newData } : field
        );

        const updatedForm = {
            ...schemaForm,
            form_fields: updatedFormFields,
        };

        setSchemaForm(updatedForm);

        const updatedActive = updatedFormFields.find((f) => f.id === currentField.id);
        if (updatedActive) {
            setActiveFields(updatedActive);
            setListOption(updatedActive.option || []);
        }

        setOptionValueAutoGen((prev) => ({ ...prev, [newId]: true }));
        setPreviousOptionLabels((prev) => ({ ...prev, [newId]: labelWithIndex }));
        setPreviousOptionValues((prev) => ({ ...prev, [newId]: newOption.value }));
    };

    const onChangeLabelCheckbox = (value: string, field: FormFieldsCheckboxOption) => {
        const getListOption = schemaForm?.form_fields.find((item) => item.id === activeFields?.id)?.option;

        const updatedOption = { ...field, label: value };

        const isAutoGenValue = optionValueAutoGen[field.id];

        if (isAutoGenValue) {
            if (value.trim() === '') {
                const prevLabel = previousOptionLabels[field.id] || '';
                const valueFromPrevLabel = generateUniqueValueFromLabel(prevLabel, field.id, getListOption || []);
                updatedOption.value = valueFromPrevLabel || field.value;
            } else {
                const newValue = generateUniqueValueFromLabel(value, field.id, getListOption || []);
                if (newValue) {
                    updatedOption.value = newValue;
                }
            }

            if (value.trim() !== '') {
                setPreviousOptionLabels((prev) => ({ ...prev, [field.id]: value }));
                setPreviousOptionValues((prev) => ({ ...prev, [field.id]: updatedOption.value }));
            }
        }

        const updateLabel = getListOption?.map((op) => (op.id === activeOption?.id ? updatedOption : op));

        const updatedForm = {
            ...schemaForm,
            form_fields: schemaForm?.form_fields.map((field) =>
                field.id === activeFields?.id ? { ...field, option: updateLabel } : field
            ),
        };

        setSchemaForm(updatedForm);
        updateLabel && setListOption(updateLabel);

        setActiveOption(updatedOption);
    };
    const onFocusValueCheckbox = (field: FormFieldsCheckboxOption) => {
        if (field.value && field.value.trim() !== '') {
            setPreviousOptionValues((prev) => ({ ...prev, [field.id]: field.value }));
        }
    };

    const onChangeValueCheckbox = (value: string, field: FormFieldsCheckboxOption) => {
        const getListOption = schemaForm?.form_fields.find((item) => item.id === activeFields?.id)?.option;
        const isDuplicate = value.trim() !== '' && !isOptionValueUnique(value, field.id, getListOption || []);

        setDuplicateValueStatus((prev) => ({ ...prev, [field.id]: isDuplicate }));

        const updateLabel = getListOption?.map((op) => (op.id === activeOption?.id ? { ...op, value: value } : op));

        const updatedForm = {
            ...schemaForm,
            form_fields: schemaForm?.form_fields.map((field) =>
                field.id === activeFields?.id ? { ...field, option: updateLabel } : field
            ),
        };

        setSchemaForm(updatedForm);
        updateLabel && setListOption(updateLabel);

        setActiveOption({ ...field, value: value });

        if (value.trim() !== '') {
            setOptionValueAutoGen((prev) => ({ ...prev, [field.id]: false }));
        }
    };

    const onBlurValueCheckbox = (field: FormFieldsCheckboxOption) => {
        const getListOption = schemaForm?.form_fields.find((item) => item.id === activeFields?.id)?.option;
        let finalValue = field.value;

        if (!field.value || field.value.trim() === '') {
            let fallbackValue = previousOptionValues[field.id];

            if (!fallbackValue) {
                if (field.label) {
                    fallbackValue = generateUniqueValueFromLabel(field.label, field.id, getListOption || []);
                }
                if (!fallbackValue) {
                    fallbackValue = 'option_1';
                }
            }
            finalValue = fallbackValue;
        } else {
            const isDuplicate = isOptionValueUnique(field.value, field.id, getListOption || []);

            if (!isDuplicate) {
                const baseValue = field.value.trim();
                let uniqueValue = baseValue;
                let counter = 1;

                while (!isOptionValueUnique(uniqueValue, field.id, getListOption || [])) {
                    uniqueValue = `${baseValue}_${counter}`;
                    counter++;
                }
                finalValue = uniqueValue;
            }
        }

        if (finalValue !== field.value) {
            const updateLabel = getListOption?.map((op) =>
                op.id === activeOption?.id ? { ...op, value: finalValue } : op
            );

            const updatedForm = {
                ...schemaForm,
                form_fields: schemaForm?.form_fields.map((formField) =>
                    formField.id === activeFields?.id ? { ...formField, option: updateLabel } : formField
                ),
            };

            setSchemaForm(updatedForm);
            updateLabel && setListOption(updateLabel);

            setActiveOption({ ...field, value: finalValue });

            setPreviousOptionValues((prev) => ({ ...prev, [field.id]: finalValue }));
        }

        setDuplicateValueStatus((prev) => ({ ...prev, [field.id]: false }));
    };

    const onSelectOption = (fields: FormFieldsCheckboxOption) => {
        setActiveOption(activeOption?.id === fields.id ? null : fields);
    };

    const onChangeFieldKey = (value: string, field: FormFields | null) => {
        if (!field || !schemaForm?.form_fields) return;

        const newKey = value.trim();

        if (activeFields?.id === field.id) {
            setActiveFields({ ...field, key: newKey });
        }
        if (!newKey) return;

        const updatedFields = schemaForm.form_fields.map((f) => (f.id === field.id ? { ...f, key: newKey } : f));

        setSchemaForm({ ...schemaForm, form_fields: updatedFields });

        setList(updatedFields);

        setFieldKeyAutoGen((prev) => ({ ...prev, [field.id]: false }));
    };

    const onBlurFieldKey = (field: FormFields | null) => {
        if (!field) return;

        const currentKey = field.key.trim();
        let newKey = currentKey;

        if (currentKey) {
            newKey = currentKey
                .replace(/\s+/g, '_')
                .replace(/[^a-zA-Z0-9_]/g, '')
                .replace(/_+/g, '_')
                .replace(/^_|_$/g, '');
        }

        if (!newKey) {
            const keyFromLabel = generateUniqueKeyFromLabel(field.label || '', field.id);
            newKey = keyFromLabel || nanoid();
        } else {
            const allFields = schemaForm?.form_fields?.filter((f) => f.id !== field.id) || [];
            const otherKeys = allFields.map((f) => f.key.trim());
            const isDuplicate = otherKeys.includes(newKey);

            if (isDuplicate) {
                let counter = 1;
                let uniqueKey = `${newKey}_${counter}`;
                while (otherKeys.includes(uniqueKey)) {
                    counter++;
                    uniqueKey = `${newKey}_${counter}`;
                }
                newKey = uniqueKey;
            }
        }

        if (newKey !== currentKey && schemaForm?.form_fields) {
            const updatedFields = schemaForm.form_fields.map((f) => (f.id === field.id ? { ...f, key: newKey } : f));
            setSchemaForm({ ...schemaForm, form_fields: updatedFields });

            if (activeFields?.id === field.id) {
                setActiveFields({ ...field, key: newKey });
            }

            setList(updatedFields);
        }
    };

    const handleChangeFormType = (newValue: SingleValue<OptionButtonType>, field: FormFields) => {
        const updatedFields = schemaForm?.form_fields.map((f) =>
            f.id === field.id ? { ...f, buttonType: newValue?.value } : f
        );
        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });
        if (activeFields?.id === field.id) {
            setActiveFields({ ...field, buttonType: newValue?.value });
        }
        setList(updatedFields);
    };

    const handleChangeFormAlignment = (newValue: SingleValue<OptionFormAlignmentType>) => {
        setSchemaForm({
            ...schemaForm,
            settings: {
                ...schemaForm?.settings,
                formAlignment: newValue?.value || 'left',
            },
        });
    };
    const handleChangePreviousForm = (newValue: MultiValue<FormFieldsCheckboxOption>) => {
        const selectedIds = newValue.map((opt) => opt.value);
        setSchemaForm({
            ...schemaForm,
            settings: {
                ...schemaForm?.settings,
                previousForm: selectedIds,
            },
        });
    };

    const handleChangeFormTitleColor = (color: string) => {
        setSchemaForm({
            ...schemaForm,
            settings: {
                ...schemaForm?.settings,
                formTitleColor: color,
            },
        });
    };

    const handleChangeFormBackgroundColor = (color: string) => {
        setSchemaForm({
            ...schemaForm,
            settings: {
                ...schemaForm?.settings,
                formBackgroundColor: color,
            },
        });
    };

    const handleChangeFieldLabelColorIndividual = (color: string, field: FormFields) => {
        const updatedFields = schemaForm?.form_fields.map((f) => (f.id === field.id ? { ...f, labelColor: color } : f));
        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });
        if (activeFields?.id === field.id) {
            setActiveFields({ ...field, labelColor: color });
        }
        setList(updatedFields);
    };

    const handleChangeFieldBackgroundColorIndividual = (color: string, field: FormFields) => {
        const updatedFields = schemaForm?.form_fields.map((f) =>
            f.id === field.id ? { ...f, backgroundColor: color } : f
        );
        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });
        if (activeFields?.id === field.id) {
            setActiveFields({ ...field, backgroundColor: color });
        }
        setList(updatedFields);
    };

    const findTypeAppearFromKey = (keyAppear: string): 'checkbox' | 'radio' | 'select' | '' => {
        if (!keyAppear || !schemaForm?.form_fields) return '';

        const targetField = schemaForm.form_fields.find((field) => field.key === keyAppear);
        if (!targetField) return '';

        switch (targetField.type) {
            case 'checkbox':
                return 'checkbox';
            case 'radio':
                return 'radio';
            case 'select':
                return 'select';
            default:
                return '';
        }
    };

    const getAppearKeyOptions = (): FormFieldsCheckboxOption[] => {
        if (!schemaForm?.form_fields || !activeFields) return [];

        return schemaForm.form_fields
            .filter(
                (field) =>
                    (field.type === 'checkbox' || field.type === 'radio' || field.type === 'select') &&
                    field.id !== activeFields.id &&
                    field.key &&
                    field.key.trim() !== ''
            )
            .map((field) => ({
                id: field.id,
                value: field.key,
                label: `${field.label || field.key} (${field.key})`,
            }));
    };

    const getAppearValueOptions = (): FormFieldsCheckboxOption[] => {
        if (!activeFields?.keyAppear || !schemaForm?.form_fields) return [];

        const targetField = schemaForm.form_fields.find((field) => field.key === activeFields.keyAppear);
        if (!targetField || !targetField.option) return [];

        return targetField.option.map((option) => ({
            id: option.id,
            value: option.value,
            label: `${option.label} (${option.value})`,
        }));
    };

    const handleChangeKeyAppear = (newValue: SingleValue<FormFieldsCheckboxOption>) => {
        const newKeyAppear = newValue?.value || '';
        const newTypeAppear = findTypeAppearFromKey(newKeyAppear);

        const updatedFields = schemaForm?.form_fields.map((f) =>
            f.id === activeFields?.id
                ? {
                      ...f,
                      keyAppear: newKeyAppear,
                      typeAppear: newTypeAppear,
                      valueAppear: '',
                  }
                : f
        );

        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });

        if (activeFields) {
            setActiveFields({
                ...activeFields,
                keyAppear: newKeyAppear,
                valueAppear: '',
            });
        }

        setList(updatedFields);
    };

    const handleChangeValueAppear = (newValue: SingleValue<FormFieldsCheckboxOption>) => {
        const newValueAppear = newValue?.value || '';

        const updatedFields = schemaForm?.form_fields.map((f) =>
            f.id === activeFields?.id ? { ...f, valueAppear: newValueAppear } : f
        );

        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });

        if (activeFields) {
            setActiveFields({ ...activeFields, valueAppear: newValueAppear });
        }

        setList(updatedFields);
    };

    const getRequiredKeyOptions = (): FormFieldsCheckboxOption[] => {
        if (!schemaForm?.form_fields || !activeFields) return [];

        return schemaForm.form_fields
            .filter(
                (field) =>
                    (field.type === 'checkbox' || field.type === 'radio' || field.type === 'select') &&
                    field.id !== activeFields.id &&
                    field.key &&
                    field.key.trim() !== ''
            )
            .map((field) => ({
                id: field.id,
                value: field.key,
                label: `${field.label || field.key} (${field.key})`,
            }));
    };

    const getRequiredValueOptions = (): FormFieldsCheckboxOption[] => {
        if (!activeFields?.keyRequired || !schemaForm?.form_fields) return [];

        const targetField = schemaForm.form_fields.find((field) => field.key === activeFields.keyRequired);
        if (!targetField || !targetField.option) return [];

        return targetField.option.map((option) => ({
            id: option.id,
            value: option.value,
            label: `${option.label} (${option.value})`,
        }));
    };

    const handleChangeKeyRequired = (newValue: SingleValue<FormFieldsCheckboxOption>) => {
        const newKeyRequired = newValue?.value || '';

        const updatedFields = schemaForm?.form_fields.map((f) =>
            f.id === activeFields?.id
                ? {
                      ...f,
                      keyRequired: newKeyRequired,
                      valueRequired: [],
                  }
                : f
        );

        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });

        if (activeFields) {
            setActiveFields({
                ...activeFields,
                keyRequired: newKeyRequired,
                valueRequired: [],
            });
        }

        setList(updatedFields);
    };

    const handleChangeValueRequired = (newValue: MultiValue<FormFieldsCheckboxOption>) => {
        const newValueRequired = newValue.map((opt) => opt.value);

        const updatedFields = schemaForm?.form_fields.map((f) =>
            f.id === activeFields?.id ? { ...f, valueRequired: newValueRequired } : f
        );

        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });

        if (activeFields) {
            setActiveFields({ ...activeFields, valueRequired: newValueRequired });
        }

        setList(updatedFields);
    };

    const handleChangeDataSource = (newValue: SingleValue<OptionFormDataSource>, field: FormFields) => {
        if (!activeFields?.dataSource) return;

        const updatedFields = schemaForm?.form_fields.map((f) =>
            f.id === field.id ? { ...f, dataSource: newValue?.value } : f
        );

        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });

        if (activeFields?.id === field.id) {
            setActiveFields({ ...field, dataSource: newValue?.value });
        }

        setList(updatedFields);
    };

    const onChangeFieldText = (value: string, field: FormFields, key: string) => {
        const processedValue = key === 'variableKey' || key === 'variableValue' ? value.trim() : value;

        const updatedFields = schemaForm?.form_fields.map((f) =>
            f.id === field.id ? { ...f, [key]: processedValue } : f
        );
        setSchemaForm({
            ...schemaForm,
            form_fields: updatedFields,
        });
        if (activeFields?.id === field.id) {
            setActiveFields({ ...field, [key]: processedValue });
        }
        setList(updatedFields);
    };

    return (
        <div>
            <p className="tw-mb-2">{isTabFields ? 'List fields' : 'Add fields'}</p>
            <div className="tw-h-[250px] tw-overflow-auto tw-border tw-border-dashed tw-rounded-lg tw-p-1">
                {isTabFields ? (
                    <ReactSortable
                        list={list}
                        setList={(newList) => {
                            const cleanedList = newList.map(cleanItem);
                            setList(cleanedList);
                            setSchemaForm({ ...schemaForm, form_fields: cleanedList });
                        }}
                        animation={200}
                        easing="ease-out"
                        scroll={true}
                        scrollSensitivity={0}
                        scrollSpeed={10}
                        ghostClass="my-ghost"
                        dragClass="my-ghost"
                    >
                        {list?.map((item, index) => (
                            <div
                                key={index}
                                className={classNames(
                                    'tw-p-2 tw-mb-2 tw-flex tw-items-center tw-gap-2 tw-bg-[#e6e6e8] tw-rounded-md tw-cursor-move tw-justify-between tw-h-[35px] tw-w-full]',
                                    {
                                        'tw-border tw-border-[#00AFF0]': activeFields?.id === item.id,
                                    }
                                )}
                                onClick={(e) => {
                                    onSelectFields(e, item);
                                }}
                            >
                                <p className="tw-truncate">{item.label}</p>
                                <X
                                    size={16}
                                    className="tw-cursor-pointer tw-flex-shrink-0 hover:tw-text-red-500"
                                    onClick={(e) => onRemoveFields(e, item.id)}
                                />
                            </div>
                        ))}
                    </ReactSortable>
                ) : (
                    <>
                        {fieldsDefault.map((item) => (
                            <div
                                className="tw-p-2 tw-mb-2 tw-flex tw-items-center tw-gap-2 tw-bg-[#e6e6e8] tw-cursor-pointer tw-rounded-md tw-justify-between hover:tw-text-[#00AFF0]"
                                key={item.id}
                                onClick={() => onAddFields(item.type as InputType)}
                            >
                                <p className="tw-truncate">{item.title}</p>
                                <Plus size={16} className="tw-cursor-pointer tw-flex-shrink-0" />
                            </div>
                        ))}
                    </>
                )}
            </div>
            <div
                className="tw-flex tw-items-center  hover:tw-text-[#00AFF0] tw-gap-1 tw-cursor-pointer tw-w-max tw-mt-2"
                onClick={onOpenAddField}
            >
                {!isTabFields && <ArrowLeft size={12} className="tw-cursor-pointer tw-flex-shrink-0" />}
                {isTabFields ? '+ Add field' : 'Back'}
            </div>{' '}
            {!activeFields && (
                <>
                    <hr className="my-0 tw-bg-[#e5e7eb] !tw-my-2" />

                    <div className="mb-1">
                        <label className="form-label tw-text-[14px]" htmlFor="formTitle">
                            Form title
                        </label>
                        <input
                            id="formTitle"
                            type="text"
                            className="form-control"
                            value={schemaForm?.settings?.title}
                            onChange={onChangeFormTitle}
                        />
                    </div>
                    <div className="my-1">
                        <label className="form-label tw-text-[14px]">Field alignment</label>
                        <Select<OptionFormAlignmentType>
                            options={optionsFormAlignment}
                            onChange={(newValue: SingleValue<OptionFormAlignmentType>) =>
                                handleChangeFormAlignment(newValue)
                            }
                            value={optionsFormAlignment.find(
                                (item) => item.value === schemaForm?.settings?.formAlignment
                            )}
                            isSearchable={false}
                            placeholder=""
                            styles={{
                                control: (base, state) => ({
                                    ...base,
                                    borderWidth: '1px',
                                    borderColor: state.isFocused ? '#00AFF0' : '#d8d6de',
                                    borderRadius: '0.375rem',
                                    boxShadow: 'none',
                                    background: 'ffffff',
                                    marginTop: '0px',
                                    '&:hover': {
                                        background: '',
                                        borderColor: '#00AFF0',
                                    },
                                    height: '38px !important',
                                }),
                                singleValue: (base) => ({
                                    ...base,
                                    color: '#6e6b7b',
                                }),
                                input: (base) => ({
                                    ...base,
                                    '&"focus': { display: 'none' },
                                }),
                                option: (base, { isFocused, isSelected }) => ({
                                    ...base,
                                    backgroundColor: isSelected ? '#e6f2ff' : isFocused ? '#e6f2ff' : undefined,
                                    color: '#6e6b7b',
                                    fontSize: '14px',
                                }),
                            }}
                        />
                    </div>
                    <div className="my-1">
                        <label className="form-label tw-text-[14px]">Previous form</label>
                        <Select<FormFieldsCheckboxOption, true>
                            isMulti
                            options={dataListForm}
                            onChange={(newValue: MultiValue<FormFieldsCheckboxOption>) =>
                                handleChangePreviousForm(newValue)
                            }
                            value={dataListForm.filter((item) =>
                                schemaForm?.settings?.previousForm?.includes(item.value)
                            )}
                            isSearchable={false}
                            placeholder=""
                            styles={{
                                control: (base, state) => ({
                                    ...base,
                                    borderWidth: '1px',
                                    borderColor: state.isFocused ? '#00AFF0' : '#d8d6de',
                                    borderRadius: '0.375rem',
                                    boxShadow: 'none',
                                    background: 'ffffff',
                                    marginTop: '0px',
                                    '&:hover': {
                                        background: '',
                                        borderColor: '#00AFF0',
                                    },
                                }),
                                singleValue: (base) => ({
                                    ...base,
                                    color: '#6e6b7b',
                                }),
                                input: (base) => ({
                                    ...base,
                                    '&"focus': { display: 'none' },
                                }),
                                option: (base, { isFocused, isSelected }) => ({
                                    ...base,
                                    backgroundColor: isSelected ? '#e6f2ff' : isFocused ? '#e6f2ff' : undefined,
                                    color: '#6e6b7b',
                                    fontSize: '14px',
                                }),
                            }}
                        />
                    </div>

                    {/* Color Settings */}
                    <div className="my-1">
                        <ColorPicker
                            label="Form Title Color"
                            value={schemaForm?.settings?.formTitleColor || '#5e5873'}
                            onChange={handleChangeFormTitleColor}
                            defaultColor="#5e5873"
                        />
                    </div>
                    <div className="my-1">
                        <ColorPicker
                            label="Form Background Color"
                            value={schemaForm?.settings?.formBackgroundColor || '#ffffff'}
                            onChange={handleChangeFormBackgroundColor}
                            defaultColor="#ffffff"
                        />
                    </div>
                </>
            )}
            <hr className="my-0 tw-bg-[#e5e7eb] !tw-my-2" />
            {activeFields && (
                <>
                    {activeFields.type !== 'free_text' &&
                        activeFields.type !== 'tooltip' &&
                        activeFields.type !== 'button' &&
                        activeFields.type !== 'proposed_date' && (
                            <div className="mb-1">
                                <label className="form-label tw-text-[14px]" htmlFor="label">
                                    Label
                                </label>
                                <input
                                    id="label"
                                    type="text"
                                    min={10}
                                    max={100}
                                    className="form-control"
                                    value={activeFields?.label || ''}
                                    onChange={(e) => activeFields && onChangeFieldLabel(e.target.value, activeFields)}
                                />
                            </div>
                        )}
                    {activeFields.type !== 'button' && (
                        <div className="mb-1">
                            <label className="form-label tw-text-[14px]" htmlFor="width">
                                Field key*
                            </label>
                            <input
                                id="width"
                                type="text"
                                className="form-control"
                                value={activeFields?.key || ''}
                                onChange={(e) => onChangeFieldKey(e.target.value, activeFields)}
                                onBlur={() => onBlurFieldKey(activeFields)}
                            />
                        </div>
                    )}

                    {activeFields.type !== 'free_text' &&
                        activeFields.type !== 'tooltip' &&
                        activeFields.type !== 'button' &&
                        activeFields.type !== 'proposed_date' && (
                            <div className="form-check form-check-inline mb-1">
                                <input
                                    className="form-check-input"
                                    type="checkbox"
                                    id="isVariable"
                                    checked={activeFields?.isVariable || false}
                                    onChange={(e) =>
                                        activeFields && onChangeFieldVariable(e.target.checked, activeFields)
                                    }
                                />
                                <label className="form-check-label" htmlFor="isVariable">
                                    Is Variable
                                </label>
                            </div>
                        )}
                    {(activeFields.type === 'text' ||
                        activeFields.type === 'number' ||
                        activeFields.type === 'date' ||
                        activeFields.type === 'date_range' ||
                        activeFields.type === 'time' ||
                        activeFields.type === 'text_area' ||
                        activeFields.type === 'checkbox' ||
                        activeFields.type === 'radio' ||
                        activeFields.type === 'select' ||
                        activeFields.type === 'file' ||
                        activeFields.type === 'area_of_implementation') && (
                        <div className="form-check form-check-inline mb-1">
                            <input
                                className="form-check-input"
                                type="checkbox"
                                id="isRequired"
                                checked={activeFields?.isRequired || false}
                                onChange={(e) => activeFields && onChangeFieldRequired(e.target.checked, activeFields)}
                            />
                            <label className="form-check-label" htmlFor="isRequired">
                                Is Required
                            </label>
                        </div>
                    )}
                    {(activeFields.type === 'text' ||
                        activeFields.type === 'number' ||
                        activeFields.type === 'date' ||
                        activeFields.type === 'time' ||
                        activeFields.type === 'text_area' ||
                        activeFields.type === 'checkbox' ||
                        activeFields.type === 'radio' ||
                        activeFields.type === 'select' ||
                        activeFields.type === 'free_text') && (
                        <div className="form-check form-check-inline mb-1">
                            <input
                                className="form-check-input"
                                type="checkbox"
                                id="isTable"
                                checked={activeFields?.isTable || false}
                                onChange={(e) => activeFields && onChangeFieldTable(e.target.checked, activeFields)}
                            />
                            <label className="form-check-label" htmlFor="isTable">
                                Use in table
                            </label>
                        </div>
                    )}

                    {activeFields.type === 'text_area' && (
                        <div className="form-check form-check-inline mb-1">
                            <input
                                className="form-check-input"
                                type="checkbox"
                                id="isRemark"
                                checked={activeFields?.isRemark || false}
                                onChange={(e) =>
                                    activeFields && onChangeFieldTableResult(e.target.checked, activeFields)
                                }
                            />
                            <label className="form-check-label" htmlFor="isRemark">
                                Use in table result
                            </label>
                        </div>
                    )}

                    <div className="mb-1">
                        <label className="form-label tw-text-[14px]" htmlFor="widthField">
                            Width (%)
                        </label>
                        <input
                            id="widthField"
                            type="number"
                            min={10}
                            max={100}
                            className="form-control"
                            value={activeFields?.width}
                            onChange={(e) => activeFields && onChangeFieldWidth(e.target.value, activeFields)}
                            onBlur={() => activeFields && onBlurFieldWidth(activeFields)}
                        />
                    </div>
                    {activeFields.type !== 'checkbox' &&
                        activeFields.type !== 'radio' &&
                        activeFields.type !== 'free_text' &&
                        activeFields.type !== 'e_smart_iso_system' &&
                        activeFields.type !== 'pha_database' &&
                        activeFields.type !== 'aims_system' &&
                        activeFields.type !== 'pssr_report' &&
                        activeFields.type !== 'button' &&
                        activeFields.type !== 'tooltip' &&
                        activeFields.type !== 'area_of_implementation' &&
                        activeFields.type !== 'select_gate_keepers' &&
                        activeFields.type !== 'validate_checklist' &&
                        activeFields.type !== 'file' && (
                            <div className="mb-1">
                                <label className="form-label tw-text-[14px]" htmlFor="height">
                                    Height (px)
                                </label>
                                <input
                                    id="height"
                                    type="number"
                                    min={10}
                                    max={100}
                                    className="form-control"
                                    value={activeFields?.height}
                                    onChange={(e) => activeFields && onChangeFieldHeight(e.target.value, activeFields)}
                                    onBlur={() => activeFields && onBlurFieldHeight(activeFields)}
                                />
                            </div>
                        )}

                    {(activeFields.type === 'checkbox' ||
                        activeFields.type === 'radio' ||
                        activeFields.type === 'select') && (
                        <div className="mb-1">
                            <label className="form-label tw-text-[14px]">Data source</label>
                            <Select<OptionFormDataSource>
                                options={optionsDataSource}
                                onChange={(newValue: SingleValue<OptionFormDataSource>) =>
                                    activeFields && handleChangeDataSource(newValue, activeFields)
                                }
                                value={optionsDataSource?.find((item) => item.value === activeFields?.dataSource)}
                                isSearchable={false}
                                placeholder=""
                                styles={{
                                    control: (base, state) => ({
                                        ...base,
                                        borderWidth: '1px',
                                        borderColor: state.isFocused ? '#00AFF0' : '#d8d6de',
                                        borderRadius: '0.375rem',
                                        boxShadow: 'none',
                                        background: 'ffffff',
                                        marginTop: '0px',
                                        '&:hover': {
                                            background: '',
                                            borderColor: '#00AFF0',
                                        },
                                        height: '38px !important',
                                    }),
                                    singleValue: (base) => ({
                                        ...base,
                                        color: '#6e6b7b',
                                    }),
                                    input: (base) => ({
                                        ...base,
                                        '&"focus': { display: 'none' },
                                    }),
                                    option: (base, { isFocused, isSelected }) => ({
                                        ...base,
                                        backgroundColor: isSelected ? '#e6f2ff' : isFocused ? '#e6f2ff' : undefined,
                                        color: '#6e6b7b',
                                        fontSize: '14px',
                                    }),
                                }}
                            />
                        </div>
                    )}

                    {activeFields.type === 'select' && (
                        <div className="mb-1">
                            <label className="form-label tw-text-[14px]" htmlFor="default_data">
                                Default data
                            </label>
                            <input
                                id="default_data"
                                type="text"
                                min={10}
                                max={100}
                                className="form-control"
                                value={activeFields?.defaultData || ''}
                                onChange={(e) => activeFields && onChangeFieldDefaultData(e.target.value, activeFields)}
                            />
                        </div>
                    )}
                    {(activeFields.type === 'checkbox' ||
                        activeFields.type === 'radio' ||
                        activeFields.type === 'select') &&
                        activeFields.dataSource === 'custom' && (
                            <div className="mb-1">
                                <label className="form-label tw-text-[14px]" htmlFor="height">
                                    Add option
                                </label>
                                <div className="tw-h-[120px] tw-overflow-auto tw-border tw-border-dashed tw-rounded-lg tw-p-1">
                                    <ReactSortable
                                        list={listOption}
                                        setList={(newList) => {
                                            const cleanedList = newList.map(cleanItem);
                                            setListOption(cleanedList);
                                            if (!activeFields) return;

                                            const updatedFormFields = schemaForm?.form_fields.map((f) =>
                                                f.id === activeFields.id ? { ...f, option: cleanedList } : f
                                            );

                                            setSchemaForm({ ...schemaForm, form_fields: updatedFormFields });

                                            const updatedActiveField = updatedFormFields.find(
                                                (f) => f.id === activeFields.id
                                            );
                                            if (updatedActiveField) {
                                                setActiveFields(updatedActiveField);
                                            }
                                        }}
                                        animation={200}
                                        easing="ease-out"
                                        scroll={true}
                                        scrollSensitivity={0}
                                        scrollSpeed={10}
                                        ghostClass="my-ghost"
                                        dragClass="my-ghost"
                                    >
                                        {listOption.map((item, index) => (
                                            <div
                                                key={index}
                                                className={classNames(
                                                    'tw-p-2 tw-mb-2 tw-flex tw-items-center tw-gap-2 tw-bg-[#e6e6e8] tw-rounded-md tw-cursor-move tw-justify-between tw-h-[35px] tw-w-full]',
                                                    {
                                                        'tw-border tw-border-[#00AFF0]': activeOption?.id === item.id,
                                                    }
                                                )}
                                                onClick={() => {
                                                    onSelectOption(item);
                                                }}
                                            >
                                                <p className="tw-truncate">{item.label}</p>
                                                <X
                                                    size={16}
                                                    className="tw-cursor-pointer tw-flex-shrink-0 hover:tw-text-red-500"
                                                    onClick={(e) => onRemoveOption(e, item.id)}
                                                />
                                            </div>
                                        ))}
                                    </ReactSortable>
                                </div>
                                <div
                                    className="tw-flex tw-items-center  hover:tw-text-[#00AFF0] tw-gap-1 tw-cursor-pointer tw-w-max tw-mt-2"
                                    onClick={onAddOption}
                                >
                                    + Add option
                                </div>
                                <hr className="my-0 tw-bg-[#e5e7eb] !tw-my-2" />
                                {activeOption && (
                                    <>
                                        <div className="mb-1">
                                            <label className="form-label tw-text-[14px]" htmlFor="option">
                                                Label
                                            </label>
                                            <input
                                                id="option"
                                                type="text"
                                                className="form-control"
                                                value={activeOption?.label}
                                                onChange={(e) => onChangeLabelCheckbox(e.target.value, activeOption)}
                                            />
                                        </div>
                                        <div className="mb-1">
                                            <label className="form-label tw-text-[14px]" htmlFor="option_value">
                                                Value (Value key is unique)
                                            </label>
                                            <input
                                                id="option_value"
                                                type="text"
                                                className={`form-control ${
                                                    duplicateValueStatus[activeOption?.id]
                                                        ? 'tw-border-red-500 tw-border-2'
                                                        : ''
                                                }`}
                                                value={activeOption?.value}
                                                onFocus={() => activeOption && onFocusValueCheckbox(activeOption)}
                                                onChange={(e) => onChangeValueCheckbox(e.target.value, activeOption)}
                                                onBlur={() => activeOption && onBlurValueCheckbox(activeOption)}
                                            />
                                            {duplicateValueStatus[activeOption?.id] && (
                                                <small className="tw-text-red-500 tw-text-xs tw-mt-1">
                                                    This value already exists. It will be made unique when you finish
                                                    editing.
                                                </small>
                                            )}
                                        </div>
                                    </>
                                )}
                            </div>
                        )}

                    {(activeFields.type === 'free_text' || activeFields.type === 'tooltip') && (
                        <div className="mb-1">
                            <label className="form-label tw-text-[14px]" htmlFor="height">
                                Content
                            </label>

                            <ReactQuillEditor
                                key={activeFields?.id}
                                value={activeFields?.label}
                                onChange={(value) => onChangeFieldText(value, activeFields, 'label')}
                                placeholder="Write content..."
                                height="350px"
                            />
                        </div>
                    )}
                    {activeFields.type !== 'button' &&
                        activeFields.type !== 'free_text' &&
                        activeFields.type !== 'proposed_date' && (
                            <div className="mb-1">
                                <label className="form-label tw-text-[14px]" htmlFor="height">
                                    Tooltip
                                </label>
                                <ReactQuillEditor
                                    key={activeFields?.id}
                                    value={activeFields?.tooltip}
                                    onChange={(value) => onChangeFieldText(value, activeFields, 'tooltip')}
                                    placeholder="Write content..."
                                    height="350px"
                                />
                            </div>
                        )}

                    {activeFields.type === 'button' && (
                        <>
                            <div className="my-1">
                                <label className="form-label tw-text-[14px]" htmlFor="content">
                                    Content
                                </label>
                                <input
                                    id="content"
                                    type="text"
                                    className="form-control"
                                    value={activeFields?.label}
                                    onChange={(e) => onChangeFieldLabel(e.target.value, activeFields)}
                                />
                            </div>
                            <div className="my-1">
                                <label className="form-label tw-text-[14px]">Button type</label>
                                <Select<OptionButtonType>
                                    options={options}
                                    onChange={(newValue: SingleValue<OptionButtonType>) =>
                                        handleChangeFormType(newValue, activeFields)
                                    }
                                    value={
                                        options?.find((item) => item.value === activeFields?.buttonType) || {
                                            value: ButtonType.CANCEL,
                                            label: 'Cancel',
                                        }
                                    }
                                    isSearchable={false}
                                    placeholder=""
                                    styles={{
                                        control: (base, state) => ({
                                            ...base,
                                            borderWidth: '1px',
                                            borderColor: state.isFocused ? '#00AFF0' : '#d8d6de',
                                            borderRadius: '0.375rem',
                                            boxShadow: 'none',
                                            background: 'ffffff',
                                            marginTop: '0px',
                                            '&:hover': {
                                                background: '',
                                                borderColor: '#00AFF0',
                                            },
                                            height: '38px !important',
                                        }),
                                        singleValue: (base) => ({
                                            ...base,
                                            color: '#6e6b7b',
                                        }),
                                        input: (base) => ({
                                            ...base,
                                            '&"focus': { display: 'none' },
                                        }),
                                        option: (base, { isFocused, isSelected }) => ({
                                            ...base,
                                            backgroundColor: isSelected ? '#e6f2ff' : isFocused ? '#e6f2ff' : undefined,
                                            color: '#6e6b7b',
                                            fontSize: '14px',
                                        }),
                                    }}
                                />
                            </div>
                            <div className="my-1">
                                <label className="form-label tw-text-[14px]" htmlFor="variableKey">
                                    Variable key
                                </label>
                                <input
                                    id="variableKey"
                                    type="text"
                                    className="form-control"
                                    value={activeFields?.variableKey}
                                    onChange={(e) => onChangeFieldText(e.target.value, activeFields, 'variableKey')}
                                />
                            </div>
                            <div className="my-1">
                                <label className="form-label tw-text-[14px]" htmlFor="variableValue">
                                    Variable value
                                </label>
                                <input
                                    id="variableValue"
                                    type="text"
                                    className="form-control"
                                    value={activeFields?.variableValue}
                                    onChange={(e) => onChangeFieldText(e.target.value, activeFields, 'variableValue')}
                                />
                            </div>
                        </>
                    )}
                    {(activeFields.type === 'text' ||
                        activeFields.type === 'number' ||
                        activeFields.type === 'date' ||
                        activeFields.type === 'date_range' ||
                        activeFields.type === 'time' ||
                        activeFields.type === 'text_area' ||
                        activeFields.type === 'checkbox' ||
                        activeFields.type === 'radio' ||
                        activeFields.type === 'select' ||
                        activeFields.type === 'free_text') && (
                        <div className="mb-1">
                            <label className="form-label tw-text-[14px]" htmlFor="isFirstAreaOwner">
                                User is used
                            </label>
                            <input
                                id="isFirstAreaOwner"
                                type="text"
                                className="form-control"
                                value={activeFields?.isFirstAreaOwner}
                                onChange={(e) =>
                                    activeFields && onChangeFieldFirstAreaOwner(e.target.value, activeFields)
                                }
                            />
                        </div>
                    )}

                    {/* Individual Field Color Settings */}
                    {activeFields.type !== 'button' &&
                        activeFields.type !== 'tooltip' &&
                        activeFields.type !== 'free_text' && (
                            <>
                                <div className="my-1">
                                    <ColorPicker
                                        label="Field Label Color"
                                        value={
                                            activeFields?.labelColor ||
                                            schemaForm?.settings?.fieldLabelColor ||
                                            '#5e5873'
                                        }
                                        onChange={(color) => handleChangeFieldLabelColorIndividual(color, activeFields)}
                                        defaultColor="#5e5873"
                                    />
                                </div>

                                <div className="my-1">
                                    <ColorPicker
                                        label="Field Background Color"
                                        value={
                                            activeFields?.backgroundColor ||
                                            schemaForm?.settings?.formBackgroundColor ||
                                            '#ffffff'
                                        }
                                        onChange={(color) =>
                                            handleChangeFieldBackgroundColorIndividual(color, activeFields)
                                        }
                                        defaultColor="#ffffff"
                                    />
                                </div>
                            </>
                        )}

                    {activeFields.type !== 'button' && (
                        <>
                            <div className="my-1">
                                <label className="form-label tw-text-[14px]">Appear key</label>
                                <Select<FormFieldsCheckboxOption>
                                    options={getAppearKeyOptions()}
                                    onChange={handleChangeKeyAppear}
                                    value={
                                        getAppearKeyOptions().find(
                                            (option) => option.value === activeFields?.keyAppear
                                        ) || null
                                    }
                                    isSearchable={false}
                                    placeholder="Select field..."
                                    isClearable
                                    styles={{
                                        control: (base, state) => ({
                                            ...base,
                                            borderWidth: '1px',
                                            borderColor: state.isFocused ? '#00AFF0' : '#d8d6de',
                                            borderRadius: '0.375rem',
                                            boxShadow: 'none',
                                            background: 'ffffff',
                                            marginTop: '0px',
                                            '&:hover': {
                                                background: '',
                                                borderColor: '#00AFF0',
                                            },
                                            height: '38px !important',
                                        }),
                                        singleValue: (base) => ({
                                            ...base,
                                            color: '#6e6b7b',
                                        }),
                                        input: (base) => ({
                                            ...base,
                                            '&"focus': { display: 'none' },
                                        }),
                                        option: (base, { isFocused, isSelected }) => ({
                                            ...base,
                                            backgroundColor: isSelected ? '#e6f2ff' : isFocused ? '#e6f2ff' : undefined,
                                            color: '#6e6b7b',
                                            fontSize: '14px',
                                        }),
                                    }}
                                />
                            </div>
                            <div className="my-1">
                                <label className="form-label tw-text-[14px]">Appear value</label>
                                <Select<FormFieldsCheckboxOption>
                                    options={getAppearValueOptions()}
                                    onChange={handleChangeValueAppear}
                                    value={
                                        getAppearValueOptions().find(
                                            (option) => option.value === activeFields?.valueAppear
                                        ) || null
                                    }
                                    isSearchable={false}
                                    placeholder={
                                        activeFields?.keyAppear ? 'Select value...' : 'Select appear key first...'
                                    }
                                    isClearable
                                    isDisabled={!activeFields?.keyAppear}
                                    styles={{
                                        control: (base, state) => ({
                                            ...base,
                                            borderWidth: '1px',
                                            borderColor: state.isFocused ? '#00AFF0' : '#d8d6de',
                                            borderRadius: '0.375rem',
                                            boxShadow: 'none',
                                            background: 'ffffff',
                                            marginTop: '0px',
                                            '&:hover': {
                                                background: '',
                                                borderColor: '#00AFF0',
                                            },
                                            height: '38px !important',
                                        }),
                                        singleValue: (base) => ({
                                            ...base,
                                            color: '#6e6b7b',
                                        }),
                                        input: (base) => ({
                                            ...base,
                                            '&"focus': { display: 'none' },
                                        }),
                                        option: (base, { isFocused, isSelected }) => ({
                                            ...base,
                                            backgroundColor: isSelected ? '#e6f2ff' : isFocused ? '#e6f2ff' : undefined,
                                            color: '#6e6b7b',
                                            fontSize: '14px',
                                        }),
                                    }}
                                />
                            </div>

                            {/* Required key and value for textarea */}
                            {activeFields.type === 'text_area' && (
                                <>
                                    <div className="my-1">
                                        <label className="form-label tw-text-[14px]">Required key</label>
                                        <Select<FormFieldsCheckboxOption>
                                            options={getRequiredKeyOptions()}
                                            onChange={handleChangeKeyRequired}
                                            value={
                                                getRequiredKeyOptions().find(
                                                    (option) => option.value === activeFields?.keyRequired
                                                ) || null
                                            }
                                            isSearchable={false}
                                            placeholder="Select field..."
                                            isClearable
                                            styles={{
                                                control: (base, state) => ({
                                                    ...base,
                                                    borderWidth: '1px',
                                                    borderColor: state.isFocused ? '#00AFF0' : '#d8d6de',
                                                    borderRadius: '0.375rem',
                                                    boxShadow: 'none',
                                                    background: 'ffffff',
                                                    marginTop: '0px',
                                                    '&:hover': {
                                                        background: '',
                                                        borderColor: '#00AFF0',
                                                    },
                                                    height: '38px !important',
                                                }),
                                                singleValue: (base) => ({
                                                    ...base,
                                                    color: '#6e6b7b',
                                                }),
                                                input: (base) => ({
                                                    ...base,
                                                    '&"focus': { display: 'none' },
                                                }),
                                                option: (base, { isFocused, isSelected }) => ({
                                                    ...base,
                                                    backgroundColor: isSelected
                                                        ? '#e6f2ff'
                                                        : isFocused
                                                        ? '#e6f2ff'
                                                        : undefined,
                                                    color: '#6e6b7b',
                                                    fontSize: '14px',
                                                }),
                                            }}
                                        />
                                    </div>
                                    <div className="my-1">
                                        <label className="form-label tw-text-[14px]">Required value</label>
                                        <Select<FormFieldsCheckboxOption, true>
                                            isMulti
                                            options={getRequiredValueOptions()}
                                            onChange={handleChangeValueRequired}
                                            value={getRequiredValueOptions().filter((option) =>
                                                activeFields?.valueRequired?.includes(option.value)
                                            )}
                                            isSearchable={false}
                                            placeholder={
                                                activeFields?.keyRequired
                                                    ? 'Select values...'
                                                    : 'Select required key first...'
                                            }
                                            isClearable
                                            isDisabled={!activeFields?.keyRequired}
                                            styles={{
                                                control: (base, state) => ({
                                                    ...base,
                                                    borderWidth: '1px',
                                                    borderColor: state.isFocused ? '#00AFF0' : '#d8d6de',
                                                    borderRadius: '0.375rem',
                                                    boxShadow: 'none',
                                                    background: 'ffffff',
                                                    marginTop: '0px',
                                                    '&:hover': {
                                                        background: '',
                                                        borderColor: '#00AFF0',
                                                    },
                                                }),
                                                singleValue: (base) => ({
                                                    ...base,
                                                    color: '#6e6b7b',
                                                }),
                                                input: (base) => ({
                                                    ...base,
                                                    '&"focus': { display: 'none' },
                                                }),
                                                option: (base, { isFocused, isSelected }) => ({
                                                    ...base,
                                                    backgroundColor: isSelected
                                                        ? '#e6f2ff'
                                                        : isFocused
                                                        ? '#e6f2ff'
                                                        : undefined,
                                                    color: '#6e6b7b',
                                                    fontSize: '14px',
                                                }),
                                            }}
                                        />
                                    </div>
                                </>
                            )}
                        </>
                    )}
                </>
            )}
        </div>
    );
};
